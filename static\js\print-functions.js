/**
 * 打印功能统一实现
 * 包含小票和水洗唛标签的所有打印相关函数
 * 统一所有页面的打印接口，确保一致性和可维护性
 */

/**
 * 格式化服务信息用于小票显示
 * @param {Object} item 衣物项目数据
 * @returns {string} 格式化后的服务信息
 */
function formatReceiptServiceInfo(item) {
    if (!item || !item.services || !Array.isArray(item.services) || item.services.length === 0) {
        return '洗衣';
    }

    // 过滤掉"加急"服务，因为它不是主要服务类型
    const mainServices = item.services.filter(s => s !== '加急');

    // 如果过滤后没有服务，返回默认的"洗衣"
    if (mainServices.length === 0) {
        return '洗衣';
    }

    // 检查是否包含加急服务
    const isUrgent = item.services.includes('加急');

    return mainServices.join('/') + (isUrgent ? '(急)' : '');
}

/**
 * 统一的小票HTML生成函数
 * @param {Object} orderData 订单数据
 * @returns {string} 小票HTML内容
 */
function generateReceiptHTML(orderData) {
    // 生成客户余额信息 - 使用专门的函数处理
    const balanceInfo = generateBalanceInfoHTML(orderData);

    return `
    <div class="receipt current-print-content">
        <div class="receipt-header">
            <h4>Soulweave改衣坊</h4>
            <p>收银小票</p>
        </div>
        <div class="receipt-info">
            <p>订单号: ${orderData.order_number}</p>
            <p>客户: ${orderData.customer_name}</p>
            <p>电话: ${orderData.customer_phone}</p>
            <p>日期: ${orderData.date}</p>
            <p>收银员: ${orderData.operator}</p>
        </div>
        <table class="receipt-items">
            <colgroup>
                <col class="col-name">
                <col class="col-quantity">
                <col class="col-service">
                <col class="col-price">
            </colgroup>
            <thead>
                <tr>
                    <th class="th-name">品名</th>
                    <th class="th-quantity">数量</th>
                    <th class="th-service">服务</th>
                    <th class="th-price">单价</th>
                </tr>
            </thead>
            <tbody>
                ${orderData.clothes.map((item, index) => `
                    <tr>
                        <td class="td-name">${(item.name || '未知')}</td>
                        <td class="td-quantity">${item.quantity || 1}</td>
                        <td class="td-service">${formatReceiptServiceInfo(item)}</td>
                        <td class="td-price">¥${(item.price || 0).toFixed(2)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
        <div class="receipt-total">
            ${orderData.discount_amount > 0 ? `
                <p>原价: ¥${(orderData.total_amount + orderData.discount_amount).toFixed(2)}</p>
                <p>折扣: -¥${orderData.discount_amount.toFixed(2)}</p>
                <p><strong>实付金额: ¥${orderData.total_amount.toFixed(2)}</strong></p>
            ` : `
                <p><strong>总金额: ¥${orderData.total_amount.toFixed(2)}</strong></p>
            `}
            <p>支付方式: ${orderData.payment_method}</p>
            ${balanceInfo}
        </div>
        <div class="receipt-footer">
            <p>感谢您的惠顾，欢迎再次光临！</p>
        </div>
    </div>
    `;
}

/**
 * 通用打印小票函数
 * 支持所有页面调用，提供统一的打印接口
 * @param {string|number|object} orderId 订单ID或订单对象
 * @param {function} showLoadingCallback 显示/隐藏加载状态的回调函数
 * @returns {Promise<void>}
 */
async function printReceipt(orderId, showLoadingCallback) {
    try {
        console.log("printReceipt函数被调用，原始参数:", orderId, "类型:", typeof orderId);

        // 正确提取订单ID - 处理可能传入对象的情况
        let actualOrderId = orderId;
        if (typeof orderId === 'object' && orderId !== null) {
            // 如果传入的是对象，尝试提取ID
            actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
            console.log("printReceipt: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
        }

        // 确保actualOrderId是有效值
        if (!actualOrderId) {
            console.error("printReceipt: 无效的订单ID:", orderId);
            throw new Error('无效的订单ID');
        }

        console.log("printReceipt: 使用的订单ID:", actualOrderId);

        // 显示加载状态
        if (typeof showLoadingCallback === 'function') {
            showLoadingCallback(true);
        }

        console.log("正在请求订单数据，URL:", `/order_details?id=${actualOrderId}`);

        // 获取订单详情数据
        const response = await fetch(`/order_details?id=${actualOrderId}`);
        console.log("API响应状态:", response.status, response.statusText);

        if (!response.ok) {
            // 尝试读取错误信息
            try {
                const errorData = await response.json();
                console.error("API错误详情:", errorData);
                throw new Error(`获取订单数据失败: ${errorData.message || response.statusText}`);
            } catch(e) {
                throw new Error(`获取订单数据失败: ${response.status} ${response.statusText}`);
            }
        }

        const orderData = await response.json();
        console.log("成功获取订单数据:", orderData);

        // 验证订单数据
        if (!orderData || !orderData.order_number) {
            console.error("无效的订单数据:", orderData);
            throw new Error('获取到的订单数据无效');
        }

        // 执行打印
        executePrint(generateReceiptHTML(orderData));

    } catch (error) {
        console.error("打印小票出错:", error);
        alert(`打印小票失败: ${error.message}`);
    } finally {
        // 隐藏加载状态
        if (typeof showLoadingCallback === 'function') {
            showLoadingCallback(false);
        }
    }
}

/**
 * 格式化服务信息用于水洗唛标签（简化版，不显示价格）
 * @param {Object} item 衣物项目数据
 * @returns {string} 格式化后的服务信息
 */
function formatLabelServiceInfo(item) {
    let services = [];

    // 如果没有服务信息，返回默认值
    if (!item || !item.services || !Array.isArray(item.services)) {
        return '洗衣';
    }

    // 添加主要服务类型（不显示价格）
    if (item.services.includes('洗衣')) {
        services.push('洗衣');
    }

    if (item.services.includes('织补')) {
        services.push('织补');
    }

    if (item.services.includes('改衣')) {
        services.push('改衣');
    }

    // 添加其他服务
    item.services.forEach(service => {
        if (!['洗衣', '织补', '改衣'].includes(service)) {
            services.push(service);
        }
    });

    // 如果没有任何服务，返回默认值
    if (services.length === 0) {
        return '洗衣';
    }

    return services.join('/');
}

/**
 * 根据订单号生成条形码
 * @param {string} orderNumber 订单号
 * @param {number} index 衣物索引
 * @returns {string} 条形码图像URL
 */
function getBarcodeForOrder(orderNumber, index) {
    // 确保订单号只包含数字，如果为空则使用默认值
    const numericOrderNumber = (orderNumber || '000000').toString().replace(/\D/g, '');

    // 确保索引是有效的数字
    const validIndex = (typeof index === 'number' && !isNaN(index)) ? index : 0;

    // 添加随机参数以确保每次都刷新条码图像
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 10000);

    // 使用后端API获取条形码
    const barcodeUrl = `/barcode/${numericOrderNumber}/${validIndex}?t=${timestamp}&r=${random}`;
    console.log("生成条形码URL:", barcodeUrl);
    return barcodeUrl;
}

/**
 * 生成客户余额信息HTML内容
 * @param {Object} orderData 订单数据
 * @returns {string} 余额信息HTML内容
 */
function generateBalanceInfoHTML(orderData) {
    // 检查多种可能的余额信息格式
    let balanceInfo = null;
    let hasBalanceAccount = false;

    // 方式1：检查 customer_balance_info 对象
    if (orderData.customer_balance_info && orderData.customer_balance_info.has_balance_account) {
        balanceInfo = orderData.customer_balance_info;
        hasBalanceAccount = true;
    }
    // 方式2：检查直接的 customer_balance 字段
    else if (orderData.customer_balance !== undefined && orderData.customer_balance !== null) {
        const beforeBalance = parseFloat(orderData.customer_balance) || 0;
        const actualAmount = parseFloat(orderData.total_amount) || 0;  // 使用total_amount作为实付金额
        const afterBalance = beforeBalance - actualAmount;

        // 只有当客户有余额账户时才显示余额信息
        if (beforeBalance > 0 || orderData.payment_method === '余额') {
            balanceInfo = {
                total_balance: beforeBalance,
                balance: beforeBalance,
                gift_balance: 0,
                balance_before_order: beforeBalance,
                balance_after_order: afterBalance,
                balance_used: orderData.payment_method === '余额' ? actualAmount : 0,
                is_balance_payment: orderData.payment_method === '余额'
            };
            hasBalanceAccount = true;
        }
    }
    // 方式3：检查是否为余额支付
    else if (orderData.payment_method === '余额') {
        // 如果是余额支付但没有余额信息，尝试从其他字段获取
        const actualAmount = parseFloat(orderData.total_amount) || 0;  // 使用total_amount作为实付金额
        balanceInfo = {
            total_balance: actualAmount,
            balance: actualAmount,
            gift_balance: 0,
            balance_before_order: actualAmount, // 假设至少有消费金额的余额
            balance_after_order: 0,
            balance_used: actualAmount,
            is_balance_payment: true
        };
        hasBalanceAccount = true;
    }

    // 如果没有余额信息，返回空字符串
    if (!hasBalanceAccount || !balanceInfo) {
        return '';
    }

    // 构建余额信息HTML
    let balanceHTML = `
        <div class="receipt-balance-info" style="margin-top: 5px; border-top: 1px dashed #000; padding-top: 3px;">
            <p style="margin: 1px 0; font-size: 11px; font-weight: bold;">客户余额信息:</p>`;

    if (balanceInfo.is_balance_payment) {
        // 余额支付时显示交易前后余额
        balanceHTML += `
            <p style="margin: 1px 0; font-size: 10px;">订单前总余额: ¥${balanceInfo.balance_before_order.toFixed(2)}</p>
            <p style="margin: 1px 0; font-size: 10px;">本次消费: ¥${balanceInfo.balance_used.toFixed(2)}</p>
            <p style="margin: 1px 0; font-size: 10px;">订单后总余额: ¥${balanceInfo.balance_after_order.toFixed(2)}</p>`;
    } else {
        // 非余额支付时显示当前余额
        balanceHTML += `
            <p style="margin: 1px 0; font-size: 10px;">当前总余额: ¥${(balanceInfo.total_balance || balanceInfo.balance_before_order).toFixed(2)}</p>`;
    }

    // 显示余额明细（如果有详细信息）
    if (balanceInfo.balance !== undefined && balanceInfo.gift_balance !== undefined) {
        balanceHTML += `
            <p style="margin: 1px 0; font-size: 9px; color: #666;">充值余额: ¥${balanceInfo.balance.toFixed(2)} | 赠送余额: ¥${balanceInfo.gift_balance.toFixed(2)}</p>`;
    }

    balanceHTML += `</div>`;

    return balanceHTML;
}

/**
 * 生成水洗唛标签HTML内容
 * @param {Array} clothesArray 衣物数组
 * @param {Object} orderData 订单数据
 * @returns {string} 水洗唛标签HTML内容
 */
function generateLabelsHTML(clothesArray, orderData) {
    // 如果没有传入orderData，则使用clothesArray的第一个元素的orderData
    if (!orderData && clothesArray && clothesArray.length > 0 && clothesArray[0].orderData) {
        orderData = clothesArray[0].orderData;
    }

    // 如果仍然没有orderData，则创建一个默认的
    if (!orderData) {
        console.warn("generateLabelsHTML: 未提供orderData参数，使用默认值");
        orderData = {
            order_number: "未知",
            customer_name: "未知",
            customer_phone: "未知",
            date: new Date().toISOString().split('T')[0],
            operator: "",
            remarks: "",
            clothes: clothesArray || []
        };
    }

    let labelHTML = '';

    clothesArray.forEach((item, index) => {
        // 判断是否有加急服务
        const isUrgent = item.services && item.services.includes('加急');
        // 判断是否有配饰
        const hasDecoration = (item.name && (item.name.includes('配饰') || item.name.includes('饰品'))) ||
                             (item.remarks && (item.remarks.includes('配饰') || item.remarks.includes('饰品')));

        // 构建服务标签HTML
        let serviceBadgesHTML = '<div class="label-service-badges">';

        // 添加服务标签
        if (item.services) {
            const serviceLabels = formatLabelServiceInfo(item).split('/');
            serviceLabels.forEach(service => {
                serviceBadgesHTML += `<span class="service-badge">${service}</span>`;
            });
        }

        // 添加加急标签
        if (isUrgent) {
            serviceBadgesHTML += '<span class="service-badge urgent">急</span>';
        }

        // 添加配饰标签
        if (hasDecoration) {
            serviceBadgesHTML += '<span class="service-badge decoration">配</span>';
        }

        serviceBadgesHTML += '</div>';

        // 创建衣物名称+价格+数量的第一行
        const firstRowHTML = `
        <div class="label-first-row">
            <div class="label-name-price">
                <span>${item.name || '未知'} (${item.color || '无'}) × ${item.quantity || 1}</span>
                <span>¥${(item.price || 0).toFixed(2)}</span>
            </div>
            <span>件: ${index+1}/${orderData.clothes ? orderData.clothes.length : clothesArray.length}</span>
        </div>`;

        // 创建客户信息行
        let customerInfo = `电: ${orderData.customer_name || '未知'}`;
        if (orderData.customer_phone) {
            customerInfo = `电: ${orderData.customer_phone.substr(0, 3)}****${orderData.customer_phone.substr(-4)} ${orderData.customer_name || '未知'}`;
        }

        // 营业员信息 - 如果存在则显示
        const operatorInfo = orderData.operator ? `营: ${orderData.operator}` : '';

        // 订单备注信息 - 如果存在则显示
        let orderRemarks = '';
        if (orderData.remarks && orderData.remarks.trim()) {
            orderRemarks = orderData.remarks.trim();
        }

        // 衣物瑕疵信息 - 如果存在则显示
        let itemFlaw = '';
        if (item.flaw && item.flaw.trim()) {
            itemFlaw = `瑕疵: ${item.flaw.trim()}`;
        }

        // 衣物备注信息 - 如果存在则显示
        let itemRemarks = '';
        if (item.remarks && item.remarks.trim()) {
            itemRemarks = item.remarks.trim();
        }

        // 生成唯一条形码
        const barcodeImageSrc = getBarcodeForOrder(orderData.order_number || "000000", index);

        // 构建服务和备注信息的组合行
        let serviceInfoRow = '<div class="label-service-info-row">';
        serviceInfoRow += serviceBadgesHTML;

        // 添加瑕疵信息
        if (itemFlaw) {
            serviceInfoRow += `<span class="label-inline-info">${itemFlaw}</span>`;
        }

        // 合并备注信息，避免重复显示"备注:"前缀
        const allRemarks = [];
        if (itemRemarks) {
            allRemarks.push(itemRemarks);
        }
        if (orderRemarks) {
            allRemarks.push(orderRemarks);
        }
        
        if (allRemarks.length > 0) {
            serviceInfoRow += `<span class="label-inline-info">备注: ${allRemarks.join(' ')}</span>`;
        }

        serviceInfoRow += '</div>';

        labelHTML += `
            <div class="wash-label" data-item-index="${index}">
                <div class="label-qr">
                    <img src="${barcodeImageSrc}" class="label-barcode" alt="条码">
                </div>
                <div class="label-info">
                    ${firstRowHTML}
                    <div>${customerInfo}</div>
                    <div>${operatorInfo} 下单: ${orderData.date ? orderData.date.split(' ')[0].replace(/-/g, '').substr(2) : '未知'}</div>
                    ${serviceInfoRow}
                </div>
            </div>
        `;
    });

    return labelHTML;
}

/**
 * 统一的打印水洗唛接口 - 供所有页面调用
 * @param {string|number|object} orderId 订单ID或订单对象
 * @param {function} showLoadingFn 显示/隐藏加载状态的回调函数
 * @param {string} selectedOption 选择的打印选项
 * @returns {Promise<void>}
 */
function printLabel(orderId, showLoadingFn = null, selectedOption = 'all') {
    console.log("统一打印水洗唛接口被调用，原始参数:", orderId, "类型:", typeof orderId);

    // 正确提取订单ID - 处理可能传入对象的情况
    let actualOrderId = orderId;
    if (typeof orderId === 'object' && orderId !== null) {
        // 如果传入的是对象，尝试提取ID
        actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
        console.log("printLabel统一接口: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
    }

    // 确保actualOrderId是有效值
    if (!actualOrderId) {
        console.error("printLabel统一接口: 无效的订单ID:", orderId);
        alert("无效的订单ID，无法打印水洗唛");
        return;
    }

    console.log("printLabel统一接口: 使用的订单ID:", actualOrderId);

    // 如果没有提供加载函数，使用默认的空函数
    const defaultShowLoading = function(show) {
        console.log(show ? "显示加载状态" : "隐藏加载状态");
    };

    const loadingFn = showLoadingFn || defaultShowLoading;

    // 调用带UI的打印函数
    return printLabelWithUI(actualOrderId, loadingFn, selectedOption);
}

/**
 * 带UI界面的水洗唛打印函数 - 用于index.html页面
 * @param {string|number|object} orderId 订单ID或订单对象
 * @param {function} showLoadingCallback 显示/隐藏加载状态的回调函数
 * @param {string} selectedOption 选择的打印选项
 * @returns {Promise<void>}
 */
async function printLabelWithUI(orderId, showLoadingCallback, selectedOption = 'all') {
    try {
        console.log("printLabelWithUI函数被调用，原始参数:", orderId, "类型:", typeof orderId);

        // 正确提取订单ID - 处理可能传入对象的情况
        let actualOrderId = orderId;
        if (typeof orderId === 'object' && orderId !== null) {
            // 如果传入的是对象，尝试提取ID
            actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
            console.log("printLabelWithUI: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
        }

        // 确保actualOrderId是有效值
        if (!actualOrderId) {
            console.error("printLabelWithUI: 无效的订单ID:", orderId);
            throw new Error('无效的订单ID');
        }

        console.log("printLabelWithUI: 使用的订单ID:", actualOrderId);

        // 显示加载状态
        if (typeof showLoadingCallback === 'function') {
            showLoadingCallback(true);
        }

        // 如果是直接打印模式，不显示UI，直接调用打印函数
        if (selectedOption === 'direct') {
            return await printLabel(actualOrderId, showLoadingCallback, 'all');
        }

        // 获取订单详情数据
        const response = await fetch(`/order_details?id=${actualOrderId}`);
        if (!response.ok) {
            throw new Error('获取订单数据失败');
        }

        const orderData = await response.json();
        console.log("成功获取订单数据:", orderData);

        // 检查是否有衣物数据
        if (!orderData.clothes || !Array.isArray(orderData.clothes) || orderData.clothes.length === 0) {
            throw new Error('订单中没有衣物数据');
        }

        // 准备水洗唛内容
        const labelContent = document.getElementById('labelContent');
        if (!labelContent) {
            console.error("找不到labelContent元素，可能是DOM结构问题");
            throw new Error('找不到水洗唛内容容器元素');
        }

        // 清空下拉选择框并添加默认选项
        const labelSelect = document.getElementById('labelSelect');
        if (!labelSelect) {
            console.error("找不到labelSelect元素，可能是DOM结构问题");
            throw new Error('找不到水洗唛选择下拉框元素');
        }

        labelSelect.innerHTML = '<option value="all">打印所有水洗唛</option>';

        // 为每件衣物添加选项
        orderData.clothes.forEach((item, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = `${index + 1}. ${item.name} (${item.color || '无'})`;
            labelSelect.appendChild(option);
        });

        // 初始显示所有水洗唛
        const initialHTML = generateLabelsHTML(orderData.clothes, orderData);
        labelContent.innerHTML = initialHTML;

        // 显示打印预览弹窗
        const modal = document.getElementById('labelModal');
        if (!modal) {
            console.error("找不到labelModal元素，可能是DOM结构问题");
            throw new Error('找不到水洗唛预览弹窗元素');
        }

        modal.style.display = 'block';

        // 打印全部水洗唛按钮事件
        const printAllBtn = document.getElementById('printAllLabelsBtn');
        if (printAllBtn) {
            // 移除旧的事件监听器
            const newPrintAllBtn = printAllBtn.cloneNode(true);
            printAllBtn.parentNode.replaceChild(newPrintAllBtn, printAllBtn);

            // 添加新的事件监听器
            newPrintAllBtn.addEventListener('click', function() {
                console.log("打印全部水洗唛按钮被点击");
                // 关闭模态框
                modal.style.display = 'none';
                // 直接执行打印，不再调用printLabel避免递归
                const allLabelsHTML = generateLabelsHTML(orderData.clothes, orderData);
                executePrint(allLabelsHTML);
            });
        }

        // 打印选定水洗唛按钮事件
        const printSelectedBtn = document.getElementById('printSelectedLabelBtn');
        if (printSelectedBtn) {
            // 移除旧的事件监听器
            const newPrintSelectedBtn = printSelectedBtn.cloneNode(true);
            printSelectedBtn.parentNode.replaceChild(newPrintSelectedBtn, printSelectedBtn);

            // 添加新的事件监听器
            newPrintSelectedBtn.addEventListener('click', function() {
                console.log("打印选中水洗唛按钮被点击");
                // 关闭模态框
                modal.style.display = 'none';

                // 获取选中的值
                const selectedValue = labelSelect.value;
                let selectedHTML;

                if (selectedValue === 'all') {
                    // 如果选择"全部"则打印所有水洗唛
                    selectedHTML = generateLabelsHTML(orderData.clothes, orderData);
                } else {
                    // 否则只打印选中的水洗唛
                    const index = parseInt(selectedValue);
                    selectedHTML = generateLabelsHTML([orderData.clothes[index]], orderData);
                }

                // 直接执行打印，不再调用printLabel避免递归
                executePrint(selectedHTML);
            });
        }

        // 下拉框变更事件
        labelSelect.onchange = function() {
            const selectedValue = this.value;
            if (selectedValue === 'all') {
                // 如果选择"全部"则显示所有水洗唛
                labelContent.innerHTML = generateLabelsHTML(orderData.clothes, orderData);
            } else {
                // 否则只显示选中的水洗唛
                const index = parseInt(selectedValue);
                labelContent.innerHTML = generateLabelsHTML([orderData.clothes[index]], orderData);
            }
        };

        return true;

    } catch (error) {
        console.error('打印水洗唛出错:', error);
        alert('打印水洗唛出错: ' + error.message);
        return false;
    } finally {
        // 隐藏加载状态
        if (typeof showLoadingCallback === 'function') {
            showLoadingCallback(false);
        }
    }
}

/**
 * 执行打印操作的通用函数
 * @param {string} htmlContent 要打印的HTML内容
 */
function executePrint(htmlContent) {
    console.log("执行打印操作，HTML内容长度:", htmlContent ? htmlContent.length : 0);

    if (!htmlContent) {
        console.error("错误：打印内容为空");
        alert("打印内容为空，无法执行打印");
        return;
    }

    // 移除之前的打印容器（如果存在）
    const oldContainer = document.getElementById('print-container');
    if (oldContainer) {
        document.body.removeChild(oldContainer);
    }

    // 移除之前的打印样式（如果存在）
    const oldStyle = document.getElementById('print-label-styles');
    if (oldStyle) {
        oldStyle.parentNode.removeChild(oldStyle);
    }

    // 创建临时打印容器
    const printContainer = document.createElement('div');
    printContainer.id = 'print-container';
    printContainer.style.position = 'fixed';  // 使用fixed定位
    printContainer.style.left = '0';          // 放在可见区域
    printContainer.style.top = '0';
    printContainer.style.width = '100%';
    printContainer.style.backgroundColor = 'white';
    printContainer.style.zIndex = '9999';     // 确保在最上层

    // 添加HTML内容到容器
    printContainer.innerHTML = htmlContent;
    document.body.appendChild(printContainer);

    // 添加内联打印样式
    const styleElement = document.createElement('style');
    styleElement.id = 'print-label-styles';
    styleElement.textContent = `
        @media print {
            /* 隐藏所有内容 */
            body * {
                visibility: hidden !important;
                display: none !important;
            }

            /* 只显示当前打印内容 */
            #print-container, #print-container * {
                visibility: visible !important;
                display: initial !important; /* 恢复默认display，避免被display:none覆盖导致空白打印 */
            }

            /* 确保打印容器可见 */
            #print-container {
                display: block !important;
            }

            /* 确保表格元素正确显示 */
            #print-container table {
                display: table !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                width: 100% !important;
            }

            #print-container colgroup {
                display: table-column-group !important;
            }

            #print-container col {
                display: table-column !important;
            }

            #print-container thead {
                display: table-header-group !important;
            }

            #print-container tbody {
                display: table-row-group !important;
            }

            #print-container tr {
                display: table-row !important;
            }

            #print-container th,
            #print-container td {
                display: table-cell !important;
                padding: 2px 1px !important;
                border: none !important;
                vertical-align: top !important;
            }

            /* 小票表格列宽定义 - 针对80mm优化 */
            #print-container .receipt-items .col-name {
                width: 35% !important;
            }

            #print-container .receipt-items .col-quantity {
                width: 12% !important;
            }

            #print-container .receipt-items .col-service {
                width: 28% !important;
            }

            #print-container .receipt-items .col-price {
                width: 25% !important;
            }

            /* 表头样式 */
            #print-container .receipt-items th {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 14px !important;
                border-bottom: 1px solid #000 !important;
                white-space: nowrap !important;
                text-align: left !important;
                color: #000 !important;
                padding: 2px 1px !important;
            }

            #print-container .receipt-items .th-quantity {
                text-align: center !important;
            }

            #print-container .receipt-items .th-price {
                text-align: right !important;
            }

            /* 表体样式 */
            #print-container .receipt-items .td-quantity {
                text-align: center !important;
            }

            #print-container .receipt-items .td-price {
                text-align: right !important;
            }

            /* 设置页面边距 - 针对80mm热敏打印纸优化 */
            @page {
                margin: 2mm !important;
                size: 80mm auto !important;
            }

            /* 定位打印内容 */
            #print-container {
                position: absolute !important;
                left: 0 !important;
                top: 0 !important;
                width: 80mm !important;
                max-width: 80mm !important;
                height: auto !important;
                background-color: white !important;
                z-index: 9999 !important;
                padding: 2mm !important;
                margin: 0 !important;
                box-sizing: border-box !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
            }

            /* 小票样式 */
            #print-container .receipt {
                width: 100% !important;
                max-width: 76mm !important;
                font-size: 22px !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                border: 1px dashed #000 !important;
                padding: 4mm !important;
                margin: 0 auto !important;
                box-sizing: border-box !important;
                page-break-after: always !important;
                color: #000 !important;
                line-height: 1.4 !important;
            }

            /* 小票标题样式 */
            #print-container .receipt-header h4 {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 26px !important;
                text-align: center !important;
                margin: 0 0 4px 0 !important;
                color: #000 !important;
                line-height: 1.3 !important;
            }

            #print-container .receipt-header p {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 20px !important;
                text-align: center !important;
                margin: 0 0 6px 0 !important;
                color: #000 !important;
                line-height: 1.3 !important;
            }

            /* 小票信息区域 */
            #print-container .receipt-info p {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 15px !important;
                margin: 3px 0 !important;
                color: #000 !important;
                line-height: 1.4 !important;
            }

            /* 小票表格优化 */
            #print-container .receipt-items {
                width: 100% !important;
                font-size: 14px !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                margin: 3mm 0 !important;
                color: #000 !important;
                line-height: 1.4 !important;
            }

            #print-container .receipt-items th {
                font-size: 14px !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                padding: 2px 1px !important;
                color: #000 !important;
                line-height: 1.3 !important;
            }

            #print-container .receipt-items td {
                font-size: 14px !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                padding: 2px 1px !important;
                word-wrap: break-word !important;
                overflow: hidden !important;
                color: #000 !important;
                line-height: 1.4 !important;
            }

            /* 小票总计区域 */
            #print-container .receipt-total {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 15px !important;
                color: #000 !important;
                margin: 3mm 0 !important;
                text-align: right !important;
                line-height: 1.4 !important;
            }

            #print-container .receipt-total p {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 15px !important;
                color: #000 !important;
                margin: 3px 0 !important;
                line-height: 1.4 !important;
            }

            /* 小票底部 */
            #print-container .receipt-footer {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 14px !important;
                color: #000 !important;
                text-align: center !important;
                margin-top: 4mm !important;
                line-height: 1.4 !important;
            }

            #print-container .receipt-footer p {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 14px !important;
                color: #000 !important;
                margin: 3px 0 !important;
                line-height: 1.4 !important;
            }

            /* 客户余额信息 */
            #print-container .receipt-balance-info {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 12px !important;
                color: #000 !important;
                margin: 3mm 0 !important;
                line-height: 1.4 !important;
            }

            #print-container .receipt-balance-info p {
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 12px !important;
                color: #000 !important;
                margin: 3px 0 !important;
                line-height: 1.4 !important;
            }

            /* 水洗唛标签样式 */
            #print-container .wash-label {
                width: 101mm !important;
                height: 16mm !important;
                max-width: 101mm !important;
                display: flex !important;
                flex-direction: row !important;
                padding: 1px 3px !important;
                margin: 0 auto 10mm auto !important;
                page-break-after: always !important;
                page-break-inside: avoid !important;
                border: 1px dashed #ccc !important;
                box-sizing: border-box !important;
                overflow: hidden !important;
                background-color: white !important;
                font-size: 10px !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                font-weight: bold !important;
                color: #000 !important;
            }

            /* 最后一个水洗唛标签不强制分页，避免空白页 */
            #print-container .wash-label:last-child {
                page-break-after: avoid !important;
                margin-bottom: 0 !important;
            }

            /* 当只有一个水洗唛时，也不强制分页 */
            #print-container .wash-label:only-child {
                page-break-after: avoid !important;
                margin-bottom: 0 !important;
            }

            #print-container .label-qr {
                text-align: center !important;
                margin-right: -4mm !important;
                margin-left: -8mm !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 63mm !important;
                padding: 0 !important;
                overflow: visible !important;
                position: relative !important;
                z-index: 1 !important;
                left: -4mm !important;
            }

            #print-container .label-barcode {
                height: 15mm !important;
                width: 63mm !important;
                object-fit: contain !important;
                margin-left: 0 !important;
                vertical-align: middle !important;
                max-width: 100% !important;
            }

            #print-container .label-info {
                flex: 1 !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: flex-start !important;
                padding-left: 0px !important;
                width: 46mm !important;
                margin-left: -8mm !important;
                font-size: 10px !important;
                line-height: 1.0 !important;
                font-weight: bold !important;
                color: #000 !important;
                position: relative !important;
                z-index: 2 !important;
                padding-right: 2mm !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
            }

            /* 确保特定元素使用flex布局 */
            #print-container .wash-label {
                display: flex !important;
                flex-direction: row !important;
            }

            #print-container .label-qr {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            #print-container .label-info {
                display: flex !important;
                flex-direction: column !important;
            }

            #print-container .label-service-badges {
                display: flex !important;
                flex-wrap: nowrap !important;
                gap: 3px !important;
                margin-top: 0px !important;
                flex-shrink: 0 !important;
                white-space: nowrap !important;
                overflow: visible !important;
            }

            #print-container .label-service-info-row {
                display: flex !important;
                align-items: center !important;
                gap: 3px !important;
                margin-top: 1px !important;
                flex-wrap: nowrap !important;
                overflow: hidden !important;
                white-space: nowrap !important;
                width: 100% !important;
                max-width: 100% !important;
            }

            #print-container .label-inline-info {
                font-size: 10px !important;
                color: #000 !important;
                margin-left: 3px !important;
                white-space: nowrap !important;
                font-weight: bold !important;
                flex-shrink: 1 !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                display: inline !important;
            }

            #print-container .label-first-row {
                display: flex !important;
                justify-content: space-between !important;
            }

            #print-container .label-name-price {
                display: flex !important;
                justify-content: space-between !important;
            }

            /* 服务标签样式 */
            #print-container .service-badge {
                display: inline-block !important;
                padding: 0px 2px !important;
                background-color: #f5f5f5 !important;
                border: 1px solid #ddd !important;
                border-radius: 2px !important;
                font-size: 10px !important;
                margin-right: 3px !important;
                margin-bottom: 0px !important;
                font-weight: bold !important;
                white-space: nowrap !important;
                font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
                color: #000 !important;
            }

            #print-container .service-badge.urgent {
                background-color: #ffeeee !important;
                border-color: #ffcccc !important;
                color: #000 !important;
                font-weight: bold !important;
            }

            #print-container .service-badge.decoration {
                background-color: #eeeeff !important;
                border-color: #ccccff !important;
                color: #000 !important;
                font-weight: bold !important;
            }

            /* 确保所有 div 在打印中保持块级布局 */
            #print-container div {
                display: block !important;
            }
        }
    `;

    // 新增: 针对不干胶标签的覆盖样式 —— 放在通用样式之后可自动覆盖
    if (htmlContent && htmlContent.indexOf('sticky-label') !== -1) {
        styleElement.textContent += `
        @media print {
            /* 72mm × 50mm 不干胶标签 */
            @page {
                size: 72mm 50mm;   /* 与预览尺寸保持一致 */
                margin: 0 !important;
            }
            /* 覆盖打印容器宽度 */
            #print-container {
                width: 72mm !important;
                max-width: 72mm !important;
                padding: 0 !important;
            }
            /* 单张标签本身 */
            #print-container .sticky-label {
                width: 72mm !important;
                height: 50mm !important;
                margin: 0 !important;
                padding: 2mm !important;
                border: 1px solid #000 !important;
                box-sizing: border-box !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: space-between !important;
                background: #fff !important;
                font-size: 13px !important;
                page-break-after: always !important;
            }
            /* 避免最后一张分页 */
            #print-container .sticky-label:last-child {
                page-break-after: avoid !important;
            }
        }
        `;
    }

    document.head.appendChild(styleElement);

    // 确保打印内容可见
    const labelElements = printContainer.querySelectorAll('.wash-label');
    if (labelElements.length > 0) {
        console.log(`找到 ${labelElements.length} 个水洗唛元素`);

        // 设置基本可见性
        labelElements.forEach((label, index) => {
            label.style.visibility = 'visible';
            label.style.display = 'flex';
            label.style.flexDirection = 'row';
            label.style.width = '101mm';
            label.style.height = '16mm';
            label.style.maxWidth = '101mm';
            label.style.padding = '1px 3px';
            label.style.margin = '0 auto 10mm auto';
            label.style.border = '1px dashed #ccc';
            label.style.boxSizing = 'border-box';
            label.style.overflow = 'hidden';
            label.style.backgroundColor = 'white';
            label.style.fontSize = '10px';
            label.style.fontFamily = '"Microsoft YaHei", "微软雅黑", Arial, sans-serif';
            label.style.fontWeight = 'bold';
            label.style.color = '#000';

            // 特殊处理：最后一个标签或唯一标签不强制分页，避免空白页
            if (index === labelElements.length - 1 || labelElements.length === 1) {
                label.style.pageBreakAfter = 'avoid';
                label.style.marginBottom = '0';
                console.log(`设置第 ${index + 1} 个标签(共${labelElements.length}个)不强制分页`);
            } else {
                label.style.pageBreakAfter = 'always';
                console.log(`设置第 ${index + 1} 个标签(共${labelElements.length}个)强制分页`);
            }

            // 设置条码区域样式
            const qrElement = label.querySelector('.label-qr');
            if (qrElement) {
                qrElement.style.visibility = 'visible';
                qrElement.style.display = 'flex';
                qrElement.style.alignItems = 'center';
                qrElement.style.justifyContent = 'center';
                qrElement.style.width = '63mm';
                qrElement.style.marginRight = '-4mm';
                qrElement.style.marginLeft = '-8mm';
                qrElement.style.left = '-4mm';
                qrElement.style.overflow = 'visible';
                qrElement.style.position = 'relative';
                qrElement.style.zIndex = '1';
            }

            // 设置条码图像样式
            const barcodeElement = label.querySelector('.label-barcode');
            if (barcodeElement) {
                barcodeElement.style.visibility = 'visible';
                barcodeElement.style.height = '15mm';
                barcodeElement.style.width = '63mm';
                barcodeElement.style.objectFit = 'contain';
                barcodeElement.style.marginLeft = '0';
                barcodeElement.style.verticalAlign = 'middle';
                barcodeElement.style.maxWidth = '100%';
            }

            // 设置信息区域样式
            const infoElement = label.querySelector('.label-info');
            if (infoElement) {
                infoElement.style.visibility = 'visible';
                infoElement.style.display = 'flex';
                infoElement.style.flexDirection = 'column';
                infoElement.style.justifyContent = 'flex-start';
                infoElement.style.flex = '1';
                infoElement.style.paddingLeft = '0px';
                infoElement.style.width = '46mm';
                infoElement.style.marginLeft = '-8mm';
                infoElement.style.fontSize = '10px';
                infoElement.style.lineHeight = '1.0';
                infoElement.style.fontWeight = 'bold';
                infoElement.style.color = '#000';
                infoElement.style.position = 'relative';
                infoElement.style.zIndex = '2';
                infoElement.style.paddingRight = '2mm';
                infoElement.style.fontFamily = '"Microsoft YaHei", "微软雅黑", Arial, sans-serif';
            }

            // 设置服务标签区域样式
            const serviceBadgesElement = label.querySelector('.label-service-badges');
            if (serviceBadgesElement) {
                serviceBadgesElement.style.visibility = 'visible';
                serviceBadgesElement.style.display = 'flex';
                serviceBadgesElement.style.flexWrap = 'nowrap';
                serviceBadgesElement.style.gap = '3px';
                serviceBadgesElement.style.marginTop = '0px';
                serviceBadgesElement.style.flexShrink = '0';
                serviceBadgesElement.style.whiteSpace = 'nowrap';
                serviceBadgesElement.style.overflow = 'visible';

                // 设置每个服务标签的样式
                const serviceBadges = serviceBadgesElement.querySelectorAll('.service-badge');
                serviceBadges.forEach(badge => {
                    badge.style.visibility = 'visible';
                    badge.style.display = 'inline-block';
                    badge.style.padding = '0px 2px';
                    badge.style.fontSize = '10px';
                    badge.style.marginRight = '3px';
                    badge.style.marginBottom = '0px';
                    badge.style.borderRadius = '2px';
                    badge.style.fontWeight = 'bold';
                    badge.style.whiteSpace = 'nowrap';
                    badge.style.fontFamily = '"Microsoft YaHei", "微软雅黑", Arial, sans-serif';

                    // 默认样式
                    badge.style.backgroundColor = '#f5f5f5';
                    badge.style.border = '1px solid #ddd';
                    badge.style.color = '#000';

                    // 特殊标签样式
                    if (badge.classList.contains('urgent')) {
                        badge.style.backgroundColor = '#ffeeee';
                        badge.style.borderColor = '#ffcccc';
                        badge.style.color = '#000';
                    } else if (badge.classList.contains('decoration')) {
                        badge.style.backgroundColor = '#eeeeff';
                        badge.style.borderColor = '#ccccff';
                        badge.style.color = '#000';
                    }
                });
            }

            // 设置第一行样式
            const firstRowElement = label.querySelector('.label-first-row');
            if (firstRowElement) {
                firstRowElement.style.visibility = 'visible';
                firstRowElement.style.display = 'flex';
                firstRowElement.style.justifyContent = 'space-between';
                firstRowElement.style.fontWeight = 'bold';
                firstRowElement.style.borderBottom = '1px solid #999';
                firstRowElement.style.paddingBottom = '1px';
                firstRowElement.style.marginBottom = '1px';
                firstRowElement.style.fontFamily = '"Microsoft YaHei", "微软雅黑", Arial, sans-serif';
                firstRowElement.style.color = '#000';
            }

            // 设置名称价格区域样式
            const namePriceElement = label.querySelector('.label-name-price');
            if (namePriceElement) {
                namePriceElement.style.visibility = 'visible';
                namePriceElement.style.display = 'flex';
                namePriceElement.style.justifyContent = 'space-between';
                namePriceElement.style.overflow = 'hidden';
                namePriceElement.style.whiteSpace = 'nowrap';
                namePriceElement.style.flex = '1';
            }

            // 设置服务信息行样式（确保服务标签和备注在同一行）
            const serviceInfoRowElement = label.querySelector('.label-service-info-row');
            if (serviceInfoRowElement) {
                serviceInfoRowElement.style.visibility = 'visible';
                serviceInfoRowElement.style.display = 'flex';
                serviceInfoRowElement.style.alignItems = 'center';
                serviceInfoRowElement.style.gap = '3px';
                serviceInfoRowElement.style.marginTop = '1px';
                serviceInfoRowElement.style.flexWrap = 'nowrap';
                serviceInfoRowElement.style.overflow = 'hidden';
                serviceInfoRowElement.style.whiteSpace = 'nowrap';
            }

            // 设置内联信息样式（瑕疵、备注等）
            const inlineInfoElements = label.querySelectorAll('.label-inline-info');
            inlineInfoElements.forEach(element => {
                element.style.visibility = 'visible';
                element.style.display = 'inline';
                element.style.fontSize = '10px';
                element.style.color = '#000';
                element.style.marginLeft = '3px';
                element.style.whiteSpace = 'nowrap';
                element.style.overflow = 'hidden';
                element.style.textOverflow = 'ellipsis';
                element.style.fontWeight = 'bold';
                element.style.flexShrink = '1';
                element.style.fontFamily = '"Microsoft YaHei", "微软雅黑", Arial, sans-serif';
            });
        });
    } else {
        console.warn("未找到水洗唛元素");
    }

    // 打印完成后的清理函数
    const cleanupPrint = function() {
        if (printContainer && printContainer.parentNode) {
            document.body.removeChild(printContainer);
        }
        if (styleElement && styleElement.parentNode) {
            document.head.removeChild(styleElement);
        }
        window.removeEventListener('afterprint', cleanupPrint);
    };

    // 添加打印后事件处理
    window.addEventListener('afterprint', cleanupPrint);

    // 延迟一点时间再打印，确保DOM已经渲染
    setTimeout(() => {
        console.log("触发打印");
        window.print();
    }, 500);

    // 如果打印对话框被取消，5秒后自动清理
    setTimeout(function() {
        if (printContainer && printContainer.parentNode) {
            cleanupPrint();
        }
    }, 5000);
}

/**
 * 执行小票打印的辅助函数 - 供历史页面等调用
 * @param {string|number|object} orderId 订单ID或订单对象
 * @param {function} showLoadingFn 显示/隐藏加载状态的回调函数
 * @returns {Promise<void>}
 */
function executePrintReceipt(orderId, showLoadingFn = null) {
    console.log("executePrintReceipt函数被调用，原始参数:", orderId, "类型:", typeof orderId);

    // 正确提取订单ID - 处理可能传入对象的情况
    let actualOrderId = orderId;
    if (typeof orderId === 'object' && orderId !== null) {
        // 如果传入的是对象，尝试提取ID
        actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
        console.log("executePrintReceipt: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
    }

    // 确保actualOrderId是有效值
    if (!actualOrderId) {
        console.error("executePrintReceipt: 无效的订单ID:", orderId);
        alert("无效的订单ID，无法打印小票");
        return;
    }

    console.log("executePrintReceipt: 使用的订单ID:", actualOrderId);

    // 如果没有提供加载函数，使用默认的空函数
    const defaultShowLoading = function(show) {
        console.log(show ? "显示加载状态" : "隐藏加载状态");
    };

    const loadingFn = showLoadingFn || defaultShowLoading;

    // 调用共享的打印小票函数
    return printReceipt(actualOrderId, loadingFn);
}

/**
 * 生成充值小票HTML内容
 * @param {Object} rechargeData 充值数据
 * @returns {string} 充值小票HTML内容
 */
function generateRechargeReceiptHTML(rechargeData) {
    const currentTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    return `
    <div class="receipt current-print-content">
        <div class="receipt-header">
            <h4>Soulweave改衣坊</h4>
            <p>账户充值小票</p>
        </div>
        <div class="receipt-info">
            <p>客户姓名: ${rechargeData.customer_name}</p>
            <p>手机号码: ${rechargeData.phone}</p>
            <p>充值时间: ${currentTime}</p>
            <p>操作员: ${rechargeData.operator || '系统'}</p>
        </div>
        <div class="receipt-recharge-details">
            <table class="receipt-items">
                <colgroup>
                    <col style="width: 40%;">
                    <col style="width: 60%;">
                </colgroup>
                <tbody>
                    <tr>
                        <td style="font-weight: bold;">充值金额:</td>
                        <td>¥${rechargeData.amount.toFixed(2)}</td>
                    </tr>
                    ${rechargeData.giftAmount > 0 ? `
                    <tr>
                        <td style="font-weight: bold;">赠送金额:</td>
                        <td style="color: #28a745;">¥${rechargeData.giftAmount.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">实际到账:</td>
                        <td style="color: #28a745;">¥${(rechargeData.amount + rechargeData.giftAmount).toFixed(2)}</td>
                    </tr>
                    ` : ''}
                    <tr>
                        <td style="font-weight: bold;">支付方式:</td>
                        <td>${rechargeData.paymentMethod}</td>
                    </tr>
                    <tr style="border-top: 1px dashed #000;">
                        <td style="font-weight: bold;">充值前余额:</td>
                        <td>¥${(rechargeData.newBalance - rechargeData.amount - rechargeData.giftAmount).toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">充值后余额:</td>
                        <td style="font-weight: bold; font-size: 14px;">¥${rechargeData.newBalance.toFixed(2)}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        ${rechargeData.isNewCustomer ? `
        <div class="receipt-new-customer">
            <p style="text-align: center; margin: 5px 0; font-weight: bold; color: #007bff;">
                🎉 欢迎新客户 🎉
            </p>
        </div>
        ` : ''}
        <div class="receipt-footer">
            <p>感谢您的信任，欢迎再次光临！</p>
            <p style="font-size: 10px; color: #666;">
                温馨提示：请保管好此小票，以备查询使用
            </p>
        </div>
    </div>
    `;
}

/**
 * 打印充值小票
 * @param {Object} rechargeData 充值数据
 */
function printRechargeReceipt(rechargeData) {
    try {
        console.log("打印充值小票，数据:", rechargeData);
        
        // 生成充值小票HTML
        const receiptHTML = generateRechargeReceiptHTML(rechargeData);
        
        // 执行打印
        executePrint(receiptHTML);
        
    } catch (error) {
        console.error("打印充值小票出错:", error);
        alert(`打印充值小票失败: ${error.message}`);
    }
}

// ==================== Lodop高级打印接口 ====================

/**
 * 显示打印方式选择对话框
 * @param {string} printType 打印类型: 'receipt', 'wash_label', 'sticky_label'
 * @param {string|number} orderId 订单ID或订单号
 * @param {string} selectedOption 选中的选项（仅水洗唛需要）
 */
function showPrintModeDialog(printType, orderId, selectedOption = 'all') {
    // 正确提取订单ID - 处理可能传入对象的情况
    let actualOrderId = orderId;
    if (typeof orderId === 'object' && orderId !== null) {
        // 如果传入的是对象，尝试提取ID
        actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
        console.log("检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
    }
    
    // 确保actualOrderId是有效值
    if (!actualOrderId) {
        console.error("无效的订单ID:", orderId);
        alert("无效的订单ID，无法进行打印");
        return;
    }
    
    console.log("显示打印模式选择对话框，订单ID:", actualOrderId, "打印类型:", printType);
    
    // 检查是否支持Lodop - 修正检查逻辑
    const supportsLodop = (typeof window.getLodopInstance === 'function') || 
                         (typeof window.checkLodop === 'function') ||
                         (typeof window.LODOP !== 'undefined') ||
                         (typeof window.CLodop !== 'undefined');
    
    let dialogHTML = `
        <div id="printModeDialog" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
             background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 30px; border-radius: 8px; max-width: 400px; width: 90%;">
                <h3 style="margin: 0 0 20px 0; text-align: center;">选择打印方式</h3>
                <div style="display: flex; flex-direction: column; gap: 15px;">
    `;
    
    if (supportsLodop) {
        dialogHTML += `
            <button onclick="executeSelectedPrint('lodop', '${printType}', '${actualOrderId}', '${selectedOption}')"
                    style="padding: 15px; background: #007BFF; color: white; border: none; border-radius: 5px; 
                           cursor: pointer; font-size: 16px;">
                🖨️ Lodop专业打印 (推荐)
            </button>
        `;
    }
    
    dialogHTML += `
        <button onclick="executeSelectedPrint('browser', '${printType}', '${actualOrderId}', '${selectedOption}')"
                style="padding: 15px; background: #28a745; color: white; border: none; border-radius: 5px; 
                       cursor: pointer; font-size: 16px;">
            📄 浏览器打印
        </button>
        <button onclick="closePrintModeDialog()"
                style="padding: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; 
                       cursor: pointer; font-size: 14px;">
            取消
        </button>
    `;
    
    dialogHTML += `
                </div>
            </div>
        </div>
    `;
    
    // 移除现有对话框（如果存在）
    const existingDialog = document.getElementById('printModeDialog');
    if (existingDialog) {
        existingDialog.remove();
    }
    
    // 添加新对话框
    document.body.insertAdjacentHTML('beforeend', dialogHTML);
}

/**
 * 关闭打印方式选择对话框
 */
function closePrintModeDialog() {
    const dialog = document.getElementById('printModeDialog');
    if (dialog) {
        dialog.remove();
    }
}

/**
 * 执行选中的打印方式
 * @param {string} mode 打印模式: 'lodop' 或 'browser'
 * @param {string} printType 打印类型: 'receipt', 'wash_label', 'sticky_label'
 * @param {string|number} orderId 订单ID或订单号
 * @param {string} selectedOption 选中的选项
 */
function executeSelectedPrint(mode, printType, orderId, selectedOption) {
    // 关闭对话框
    closePrintModeDialog();
    
    // 正确提取订单ID - 处理可能传入对象的情况
    let actualOrderId = orderId;
    if (typeof orderId === 'object' && orderId !== null) {
        // 如果传入的是对象，尝试提取ID
        actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
        console.log("executeSelectedPrint: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
    }
    
    // 确保actualOrderId是有效值
    if (!actualOrderId) {
        console.error("executeSelectedPrint: 无效的订单ID:", orderId);
        alert("无效的订单ID，无法进行打印");
        return;
    }
    
    console.log("执行打印，模式:", mode, "类型:", printType, "订单ID:", actualOrderId);
    
    try {
        if (mode === 'lodop') {
            // 使用Lodop打印
            switch (printType) {
                case 'receipt':
                    if (typeof window.lodopPrintReceipt === 'function') {
                        window.lodopPrintReceipt(actualOrderId);
                    } else {
                        throw new Error('Lodop小票打印功能未加载');
                    }
                    break;
                    
                case 'wash_label':
                    if (typeof window.lodopPrintWashLabels === 'function') {
                        window.lodopPrintWashLabels(actualOrderId, selectedOption);
                    } else {
                        throw new Error('Lodop水洗唛打印功能未加载');
                    }
                    break;
                    
                case 'sticky_label':
                    if (typeof window.lodopPrintStickyLabel === 'function') {
                        window.lodopPrintStickyLabel(actualOrderId);
                    } else {
                        throw new Error('Lodop不干胶标签打印功能未加载');
                    }
                    break;
                    
                default:
                    throw new Error('未知的打印类型: ' + printType);
            }
        } else if (mode === 'browser') {
            // 使用浏览器打印
            switch (printType) {
                case 'receipt':
                    printReceipt(actualOrderId);
                    break;
                    
                case 'wash_label':
                    printLabel(actualOrderId, null, selectedOption);
                    break;
                    
                case 'sticky_label':
                    printStickyLabelBrowser(actualOrderId);
                    break;
                    
                default:
                    throw new Error('未知的打印类型: ' + printType);
            }
        } else {
            throw new Error('未知的打印模式: ' + mode);
        }
    } catch (error) {
        console.error('打印执行失败:', error);
        alert('打印失败: ' + error.message);
    }
}

/**
 * 统一的小票打印入口 - 支持选择打印方式
 * @param {string|number|object} orderId 订单ID或订单对象
 */
function printReceiptWithModeSelection(orderId) {
    console.log("printReceiptWithModeSelection被调用，参数:", orderId);
    showPrintModeDialog('receipt', orderId);
}

/**
 * 统一的水洗唛打印入口 - 支持选择打印方式
 * @param {string|number|object} orderId 订单ID或订单对象
 * @param {string} selectedOption 选中的选项
 */
function printWashLabelWithModeSelection(orderId, selectedOption = 'all') {
    console.log("printWashLabelWithModeSelection被调用，参数:", orderId, selectedOption);
    showPrintModeDialog('wash_label', orderId, selectedOption);
}

/**
 * 统一的不干胶标签打印入口 - 支持选择打印方式
 * @param {string|object} orderNumber 订单号或订单对象
 */
function printStickyLabelWithModeSelection(orderNumber) {
    console.log("printStickyLabelWithModeSelection被调用，参数:", orderNumber);
    showPrintModeDialog('sticky_label', orderNumber);
}

// 兼容性函数 - 保持向后兼容
window.printReceiptWithModeSelection = printReceiptWithModeSelection;
window.printWashLabelWithModeSelection = printWashLabelWithModeSelection;
window.printStickyLabelWithModeSelection = printStickyLabelWithModeSelection;

console.log('打印功能增强版本加载完成 - 支持Lodop和浏览器双模式打印');

// --------------- 不干胶标签(浏览器) ----------------
/**
 * 生成不干胶标签 HTML
 * @param {Object} labelData 标签数据
 * @returns {string}
 */
function generateStickyLabelHTML(labelData, index = 0){
    if(!labelData) return '';

    /* 工具函数：生成一行左右分列（兼容打印，不依赖 flex） */
    const row = (left,right='',marginTop='5.5mm')=>
        `<div style="display:block;width:100%;margin-top:${marginTop};font-size:14px;line-height:1.35;">
            <span style="display:inline-block;">${left}</span>
            ${right?`<span style="float:right;">${right}</span>`:''}
        </div>`;

    // 日期 yyMMdd
    let dateStr='';
    if(labelData.date){
        const d=new Date(labelData.date);
        const y=d.getFullYear().toString().slice(-2);
        const m=(d.getMonth()+1).toString().padStart(2,'0');
        const day=d.getDate().toString().padStart(2,'0');
        dateStr=`${y}${m}${day}`;
    }

    const phoneDisplay=labelData.customer_phone?`${labelData.customer_phone.slice(0,3)}****${labelData.customer_phone.slice(-4)}`:'未知';

    const clothes=labelData.clothes||[];
    const clothesNames=clothes.map((c,i)=>`${i+1}.${c.name}`).join(' ');

    // 徽章
    const badgeArr=[];
    if(clothes.some(c=>c.has_accessory==='true'||/配饰|饰品/.test(c.name||''))) badgeArr.push('[配]');
    if(clothes.some(c=>c.is_urgent==='true'||(c.services||[]).includes('加急'))) badgeArr.push('[急]');
    const badgeStr=badgeArr.join(' ');

    return `
    <div class="sticky-label current-print-content" style="width:75mm;border:1px dashed #000;padding:7mm;font-family:'Microsoft YaHei',Arial;font-size:14px;box-sizing:border-box;">
        <div style="text-align:center;font-weight:bold;font-size:20px;line-height:1.4;">营业员: ${labelData.operator||'未知'}</div>
        ${row(`单号: ${labelData.order_number}`,`测试日期: ${dateStr}`,'5.5mm')}
        ${row(`电话: ${phoneDisplay}`,badgeStr,'5.5mm')}
        ${row(`客户: ${labelData.customer_name||'未知'}`,`总件数: ${clothes.length}`,'5.5mm')}
        <div style="margin-top:5.5mm;display:block;font-size:14px;line-height:1.3;">衣物: ${clothesNames}</div>
        <div style="text-align:center;margin-top:7mm;"><img src="/barcode/${labelData.order_number}/${index}" style="width:80mm;height:20mm;"/></div>
    </div>`;
}

/**
 * 浏览器打印不干胶标签
 * @param {string} orderNumber 订单号
 * @param {function|null} showLoadingCallback 加载回调
 */
async function printStickyLabelBrowser(orderNumber,showLoadingCallback=null){
    try{
        console.log('浏览器不干胶打印开始',orderNumber);
        if(typeof showLoadingCallback==='function')showLoadingCallback(true);
        const resp=await fetch(`/api/order_label/${orderNumber}`);
        if(!resp.ok)throw new Error('获取标签数据失败');
        const result=await resp.json();
        if(!result.success)throw new Error(result.message||'获取标签数据失败');

        let orderData = result.label_data;

        // 若接口未返回衣物列表，则再次调用 /order_details 获取完整信息
        if(!orderData.clothes || !Array.isArray(orderData.clothes) || orderData.clothes.length===0){
            try{
                const detailResp = await fetch(`/order_details?id=${orderData.order_number}`);
                if(detailResp.ok){
                    const detail = await detailResp.json();
                    if(detail && Array.isArray(detail.clothes)){
                        orderData.clothes = detail.clothes;
                    }
                }
            }catch(err){
                console.warn('获取订单详情失败，继续使用现有数据',err);
            }
        }

        const html=generateStickyLabelsHTML(orderData);
        if(!html)throw new Error('生成标签失败');
        executePrint(html);
    }catch(e){
        console.error('浏览器不干胶打印失败',e);
        alert('浏览器不干胶打印失败:'+e.message);
    }finally{
        if(typeof showLoadingCallback==='function')showLoadingCallback(false);
    }
}

/**
 * 根据订单数据批量生成不干胶标签 HTML
 * 与水洗唛逻辑一致：一件衣物一张标签，多件时自动分页
 * @param {Object} orderData 包含 clothes 数组
 * @returns {string}
 */
function generateStickyLabelsHTML(orderData){
    if(!orderData) return '';
    const total = (orderData.clothes && orderData.clothes.length) ? orderData.clothes.length : 1;
    let html = '';
    for(let i = 0; i < total; i++){
        html += generateStickyLabelHTML(orderData, i);
    }
    return html;
}
