/**
 * Lodop打印集成模块
 * 统一处理水洗唛、不干胶标签、小票的Lodop打印功能
 * 版本: 1.0.0
 * 创建日期: 2024-03-15
 */

// Lodop打印集成模块
console.log('Lodop打印集成模块已加载');

// 全局变量
let LODOP_INSTANCE = null;

// 获取Lodop实例
function getLodopInstance() {
    if (window.LODOP && window.LODOP.VERSION) {
        LODOP_INSTANCE = window.LODOP;
        return LODOP_INSTANCE;
    }
    
    if (typeof CLodop !== 'undefined' && CLodop) {
        LODOP_INSTANCE = CLodop();
        return LODOP_INSTANCE;
    }
    
    return null;
}

// 检查Lodop是否可用
function checkLodopAvailable() {
    const lodop = getLodopInstance();
    if (!lodop) {
        alert('打印控件未安装或未正确加载，请先安装Lodop打印控件');
        return false;
    }
    return true;
}

/**
 * 水洗唛标签打印 (101mm × 16mm) - 根据图片示例优化布局
 * @param {Object} orderData 订单数据
 * @param {Array} selectedItems 选中的衣物项目，如果为空则打印全部
 */
function printWashLabels(orderData, selectedItems = null) {
    if (!checkLodopAvailable()) return;

    try {
        // 确定要打印的衣物
        const itemsToPrint = selectedItems || orderData.clothes || [];
        
        if (itemsToPrint.length === 0) {
            alert('没有要打印的衣物项目');
            return;
        }

        // 初始化打印任务
        const LODOP = getLodopInstance();
        LODOP.PRINT_INIT("水洗唛标签打印");
        
        // 设置纸张大小 - 确保使用正确的101mm × 16mm尺寸
        LODOP.SET_PRINT_PAGESIZE(1, "101mm", "16mm", "");
        
        // 设置打印模式
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREIEW", true);

        // 为每个衣物项目生成水洗唛
        itemsToPrint.forEach((item, index) => {
            if (index > 0) {
                LODOP.NEWPAGE(); // 新建页面
            }
            
            // 绘制外边框
            LODOP.ADD_PRINT_RECT("0.5mm", "0.5mm", "100mm", "15mm", 0, 1);
            
            // 左侧条形码区域 (约40mm宽)
            // 条形码
            const barcodeContent = `${orderData.order_number}-${String(index + 1).padStart(2, '0')}`;
            LODOP.ADD_PRINT_BARCODE(
                "2mm",   // Top - 距顶部1.5mm
                "3mm",     // Left - 距左边1mm  
                "38mm",    // Width - 条形码宽度38mm
                "12mm",    // Height - 条形码高度12mm
                "128A",    // 条码类型
                barcodeContent
            );
            
            // 设置条码样式
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 6);
            LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 1); // 显示条码文字
            
            // 右侧信息区域 (从40mm开始到99mm)
            // 绘制信息区域分隔线
            LODOP.ADD_PRINT_LINE("0.5mm", "40mm", "15.5mm", "40mm", 0, 1);
            
            // 第一行：衣物名称和数量、价格
            const itemName = `${item.name || '未知'}${item.color ? `(${item.color})` : ''}`;
            const quantity = item.quantity || 1;
            const priceText = `¥${(item.price || 0).toFixed(2)}件 ${quantity}/1`;
            
            // 衣物名称 (左侧)
            LODOP.ADD_PRINT_TEXT("1mm", "41mm", "35mm", "3.5mm", itemName);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // 价格信息 (右侧)
            LODOP.ADD_PRINT_TEXT("1mm", "76mm", "23mm", "3.5mm", priceText);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐
            
            // 第二行：客户信息
            const phoneDisplay = orderData.customer_phone ? 
                `电: ${orderData.customer_phone.substr(0, 3)}****${orderData.customer_phone.substr(-4)}` : 
                '电: 未知';
            const customerName = orderData.customer_name || '未知';
            const customerInfo = `${phoneDisplay} ${customerName}`;
            
            LODOP.ADD_PRINT_TEXT("4.5mm", "41mm", "58mm", "3mm", customerInfo);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 7);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // 第三行：订单号和日期
            const dateFormatted = orderData.date ? 
                orderData.date.split(' ')[0].replace(/-/g, '').substr(2) : 
                '未知';
            const orderInfo = `营: ${orderData.operator || '未知'} 下单: ${dateFormatted}`;
            
            LODOP.ADD_PRINT_TEXT("7.5mm", "41mm", "58mm", "3mm", orderInfo);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 7);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // 第四行：服务和备注信息
            let serviceAndRemarks = '';
            
            // 添加服务信息
            if (item.services && Array.isArray(item.services)) {
                const services = item.services.filter(s => s !== '加急');
                if (services.length > 0) {
                    serviceAndRemarks += `改衣  备注: ${services.join('/')}`;
                }
                
                // 加急标记
                if (item.services.includes('加急')) {
                    serviceAndRemarks += ' [急]';
                }
            } else {
                serviceAndRemarks = '改衣  备注: ';
            }
            
            // 配饰标记
            if (item.name && (item.name.includes('配饰') || item.name.includes('饰品'))) {
                serviceAndRemarks += ' [配]';
            }
            
            // 瑕疵和备注信息
            const remarks = [];
            if (item.flaw && item.flaw.trim()) {
                remarks.push(`瑕疵:${item.flaw.trim()}`);
            }
            if (item.remarks && item.remarks.trim()) {
                remarks.push(item.remarks.trim());
            }
            if (orderData.remarks && orderData.remarks.trim()) {
                remarks.push(orderData.remarks.trim());
            }
            
            if (remarks.length > 0) {
                if (!serviceAndRemarks.includes('备注:')) {
                    serviceAndRemarks += ` 备注: ${remarks.join(' ')}`;
                } else {
                    serviceAndRemarks += remarks.join(' ');
                }
            }
            
            // 限制文本长度，避免超出标签范围
            if (serviceAndRemarks.length > 60) {
                serviceAndRemarks = serviceAndRemarks.substring(0, 57) + '...';
            }
            
            LODOP.ADD_PRINT_TEXT("10.5mm", "41mm", "58mm", "4mm", serviceAndRemarks);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 6);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // 急件标志 (在右上角显示)
            if (item.services && item.services.some(s => s.includes('急件') || s.includes('加急'))) {
                LODOP.ADD_PRINT_TEXT("1mm", "95mm", "5mm", "3mm", "[急]");
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "FontColor", "#FF0000");
            }
        });
        
        // 显示打印预览
        LODOP.PREVIEW();
        
    } catch (error) {
        console.error('水洗唛打印失败:', error);
        alert('水洗唛打印失败: ' + error.message);
    }
}

/**
 * 不干胶标签打印 (72mm × 50mm)
 * @param {Object} labelData 标签数据
 */
function printStickyLabel(labelData) {
    if (!checkLodopAvailable()) return;

    try {
        const LODOP = getLodopInstance();
        LODOP.PRINT_INIT("不干胶标签打印");
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREVIEW", true);

        // 纸张尺寸 72mm×50mm
        LODOP.SET_PRINT_PAGESIZE(1, "72mm", "50mm", "");

        // 如果没有衣物列表，则按 1 处理
        const totalItems = (labelData.clothes && labelData.clothes.length) ? labelData.clothes.length : 1;

        // 统一计算衣物名称串、徽章等（网页布局使用整单信息）
        const clothesNames = (labelData.clothes || []).map((c, i) => `${i + 1}.${c.name || '未知'}`).join(' ');
        const totalCount = (labelData.clothes || []).length;
        const badgeArr = [];
        if ((labelData.clothes || []).some(c => c.has_accessory === 'true' || (/配饰|饰品/.test(c.name || '')))) badgeArr.push('[配]');
        if ((labelData.clothes || []).some(c => c.is_urgent === 'true' || ((c.services || []).includes && (c.services || []).includes('加急')))) badgeArr.push('[急]');
        const badgeTextGlobal = badgeArr.join(' ');

        // 辅助函数：绘制单张标签
        const drawSingleLabel = (idx) => {
            // 外边框 - 改为虚线，与网页 dashed 风格一致 (LineStyle=2)
            LODOP.ADD_PRINT_RECT("1mm", "1mm", "70mm", "48mm", 2, 1);

            // 头部：营业员信息，居中加粗，20px≈14pt
            LODOP.ADD_PRINT_TEXT("4mm", "1mm", "70mm", "6mm", `营业员: ${labelData.operator || '未知'}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 14);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);

            // 日期格式 yyMMdd
            const dateFormatted = labelData.date ? labelData.date.split(' ')[0].replace(/-/g, '').substr(2) : '未知';

            // 行 1：单号 / 日期
            LODOP.ADD_PRINT_TEXT("12mm", "3mm", "30mm", "4mm", `单号: ${labelData.order_number}`);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.ADD_PRINT_TEXT("12mm", "40mm", "29mm", "4mm", `日期: ${dateFormatted}`);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 3);

            // 行 2：电话 / 徽章
            console.log('调试电话号码:', labelData.customer_phone, '类型:', typeof labelData.customer_phone);
            const phoneDisplay = (labelData.customer_phone && String(labelData.customer_phone).trim()) ? String(labelData.customer_phone) : '未知';
            console.log('处理后的电话显示:', phoneDisplay);
            
            LODOP.ADD_PRINT_TEXT("17mm", "3mm", "36mm", "4mm", `电话: ${phoneDisplay}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            if (badgeTextGlobal) {
                LODOP.ADD_PRINT_TEXT("17mm", "40mm", "29mm", "4mm", badgeTextGlobal);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3);
            }

            // 行 3：客户 / 总件数
            LODOP.ADD_PRINT_TEXT("22mm", "3mm", "30mm", "4mm", `客户: ${labelData.customer_name || '未知'}`);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.ADD_PRINT_TEXT("22mm", "40mm", "29mm", "4mm", `总件数: ${totalCount}`);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 3);

            // 行 4：衣物列表（靠左对齐）
            LODOP.ADD_PRINT_TEXT("28mm", "3mm", "66mm", "6mm", `衣物: ${clothesNames}`);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);

            // 条形码（订单号-索引，两位）
            const barcodeContent = `${labelData.order_number}-${String(idx + 1).padStart(2, '0')}`;
            LODOP.ADD_PRINT_BARCODE("37mm", "16mm", "40mm", "10mm", "128A", barcodeContent);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 1);
        };

        for (let i = 0; i < totalItems; i++) {
            if (i > 0) {
                LODOP.NEWPAGE();
            }
            drawSingleLabel(i);
        }

        // 预览
        LODOP.PREVIEW();

    } catch (error) {
        console.error('不干胶标签打印失败:', error);
        alert('不干胶标签打印失败: ' + error.message);
    }
}

/**
 * 小票打印 (80mm 热敏纸)
 * @param {Object} orderData 订单数据
 */
function printReceiptLodop(orderData) {
    if (!checkLodopAvailable()) return;

    try {
        // 初始化打印任务
        const LODOP = getLodopInstance();
        LODOP.PRINT_INIT("收银小票打印");
        
        // 设置纸张大小 - 80mm宽度，高度自适应
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", "200mm", "");
        
        // 设置打印模式
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREIEW", true);
        
        let currentTop = 5; // 当前打印位置 (mm)
        
        // 小票头部
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "8mm", "Soulweave改衣坊");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 18);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        currentTop += 10;
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "收银小票");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 14);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        currentTop += 8;
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;
        
        // 订单信息
        const orderInfo = [
            `订单号: ${orderData.order_number}`,
            `客户: ${orderData.customer_name}`,
            `电话: ${orderData.customer_phone}`,
            `日期: ${orderData.date}`,
            `收银员: ${orderData.operator}`
        ];
        
        orderInfo.forEach(info => {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", info);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
        });
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;
        
        // 表格头部
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "25mm", "5mm", "品名");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "30mm", "10mm", "5mm", "数量");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "40mm", "20mm", "5mm", "服务");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "60mm", "15mm", "5mm", "单价");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐
        currentTop += 6;
        
        // 表格分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 2;
        
        // 商品列表
        if (orderData.clothes && Array.isArray(orderData.clothes)) {
            orderData.clothes.forEach(item => {
                // 商品名称
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "25mm", "5mm", item.name || '未知');
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                
                // 数量
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "30mm", "10mm", "5mm", (item.quantity || 1).toString());
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
                
                // 服务信息
                let serviceText = '洗衣';
                if (item.services && Array.isArray(item.services)) {
                    const mainServices = item.services.filter(s => s !== '加急');
                    if (mainServices.length > 0) {
                        serviceText = mainServices.join('/');
                    }
                    if (item.services.includes('加急')) {
                        serviceText += '(急)';
                    }
                }
                
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "40mm", "20mm", "5mm", serviceText);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                
                // 单价
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "60mm", "15mm", "5mm", `¥${(item.price || 0).toFixed(2)}`);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐
                
                currentTop += 6;
            });
        }
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;
        
        // 合计信息
        if (orderData.discount_amount && orderData.discount_amount > 0) {
            // 有折扣的情况
            const originalAmount = (parseFloat(orderData.total_amount) || 0) + (parseFloat(orderData.discount_amount) || 0);
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `原价: ¥${(originalAmount).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `折扣: -¥${(parseFloat(orderData.discount_amount)||0).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `实付金额: ¥${(parseFloat(orderData.total_amount)||0).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 7;
        } else {
            // 无折扣的情况
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `总金额: ¥${(parseFloat(orderData.total_amount)||0).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 7;
        }
        
        // 支付方式
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `支付方式: ${orderData.payment_method || '现金'}`);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 8;
        
        // 客户余额信息（如果有）
        if (orderData.customer_balance_info && orderData.customer_balance_info.has_balance_account) {
            const balanceInfo = orderData.customer_balance_info;
            
            // 虚线分隔
            LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 2, 1);
            currentTop += 3;
            
            const beforeBal = parseFloat(balanceInfo.balance_before || balanceInfo.total_balance || 0) || 0;
            const consumed = parseFloat(balanceInfo.consumed_amount || balanceInfo.balance_used || 0) || 0;
            const afterBal = parseFloat(balanceInfo.balance_after || balanceInfo.balance_after_order || 0) || 0;

            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `消费前余额: ¥${beforeBal.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 5;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `本次消费: ¥${consumed.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 5;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `剩余余额: ¥${afterBal.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 8;
        }
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 2, 1);
        currentTop += 3;
        
        // 小票底部
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", "感谢您的惠顾，欢迎再次光临！");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        currentTop += 8;
        
        // 设置实际页面高度
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", `${currentTop + 5}mm`, "");
        
        // 显示打印预览
        LODOP.PREVIEW();
        
    } catch (error) {
        console.error('小票打印失败:', error);
        alert('小票打印失败: ' + error.message);
    }
}

/**
 * 统一的Lodop水洗唛打印接口
 * @param {string|number|object} orderId 订单ID或订单对象
 * @param {string} selectedOption 选择选项，'all'打印全部，数字打印特定索引
 */
async function lodopPrintWashLabels(orderId, selectedOption = 'all') {
    try {
        console.log("Lodop水洗唛打印开始，原始参数:", orderId, "类型:", typeof orderId, "选项:", selectedOption);
        
        // 正确提取订单ID - 处理可能传入对象的情况
        let actualOrderId = orderId;
        if (typeof orderId === 'object' && orderId !== null) {
            // 如果传入的是对象，尝试提取ID
            actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
            console.log("lodopPrintWashLabels: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
        }

        // 确保actualOrderId是有效值
        if (!actualOrderId) {
            console.error("lodopPrintWashLabels: 无效的订单ID:", orderId);
            throw new Error('无效的订单ID');
        }

        console.log("lodopPrintWashLabels: 使用的订单ID:", actualOrderId);
        
        // 修正API路径 - 使用正确的order_details路由
        const response = await fetch(`/order_details?id=${actualOrderId}`);
        if (!response.ok) {
            throw new Error('获取订单数据失败');
        }
        
        const result = await response.json();
        console.log("获取到查询结果:", result);
        
        // 从查询结果中提取订单数据 - order_details返回的是直接的订单数据
        if (result.error) {
            throw new Error(result.error);
        }
        
        const orderData = result; // order_details直接返回订单数据
        console.log("提取的订单数据:", orderData);
        
        // 检查衣物数据
        if (!orderData.clothes || !Array.isArray(orderData.clothes) || orderData.clothes.length === 0) {
            throw new Error('订单中没有衣物数据');
        }
        
        // 确定要打印的衣物
        let selectedItems = null;
        if (selectedOption !== 'all') {
            const index = parseInt(selectedOption);
            if (!isNaN(index) && index >= 0 && index < orderData.clothes.length) {
                selectedItems = [orderData.clothes[index]];
            }
        }
        
        // 执行Lodop打印
        printWashLabels(orderData, selectedItems);
        
    } catch (error) {
        console.error('Lodop水洗唛打印失败:', error);
        alert('Lodop水洗唛打印失败: ' + error.message);
    }
}

/**
 * 统一的Lodop小票打印接口
 * @param {string|number|object} orderId 订单ID或订单对象
 */
async function lodopPrintReceipt(orderId) {
    try {
        console.log("Lodop小票打印开始，原始参数:", orderId, "类型:", typeof orderId);
        
        // 正确提取订单ID - 处理可能传入对象的情况
        let actualOrderId = orderId;
        if (typeof orderId === 'object' && orderId !== null) {
            // 如果传入的是对象，尝试提取ID
            actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
            console.log("lodopPrintReceipt: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
        }

        // 确保actualOrderId是有效值
        if (!actualOrderId) {
            console.error("lodopPrintReceipt: 无效的订单ID:", orderId);
            throw new Error('无效的订单ID');
        }

        console.log("lodopPrintReceipt: 使用的订单ID:", actualOrderId);
        
        // 修正API路径 - 使用正确的order_details路由
        const response = await fetch(`/order_details?id=${actualOrderId}`);
        if (!response.ok) {
            throw new Error('获取订单数据失败');
        }
        
        const result = await response.json();
        console.log("获取到查询结果:", result);
        
        // 从查询结果中提取订单数据 - order_details返回的是直接的订单数据
        if (result.error) {
            throw new Error(result.error);
        }
        
        const orderData = result; // order_details直接返回订单数据
        console.log("提取的订单数据:", orderData);
        
        // 调用专用的小票打印函数，避免与网页函数重名
        printReceiptLodop(orderData);
        
    } catch (error) {
        console.error('Lodop小票打印失败:', error);
        alert('Lodop小票打印失败: ' + error.message);
    }
}

/**
 * 通用的Lodop不干胶标签打印接口
 * @param {string} orderNumber 订单号
 */
async function lodopPrintStickyLabel(orderNumber) {
    try {
        console.log("Lodop不干胶标签打印开始，订单号:", orderNumber);
        
        // 获取标签数据
        const response = await fetch(`/api/order_label/${orderNumber}`);
        if (!response.ok) {
            throw new Error('获取标签数据失败');
        }
        
        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || '获取标签数据失败');
        }
        
        const labelData = result.label_data;
        console.log("获取到标签数据:", labelData);
        
        // 执行Lodop打印
        printStickyLabel(labelData);
        
    } catch (error) {
        console.error('Lodop不干胶标签打印失败:', error);
        alert('Lodop不干胶标签打印失败: ' + error.message);
    }
}

// 导出函数供全局使用
window.lodopPrintWashLabels = lodopPrintWashLabels;
window.lodopPrintReceipt = lodopPrintReceipt;
window.lodopPrintStickyLabel = lodopPrintStickyLabel;

console.log('Lodop打印集成模块初始化完成');

// 水洗标签打印函数 (101mm × 16mm) - 与printWashLabels保持一致的布局
function printWashLabelLodop(orderData) {
    if (!checkLodopAvailable()) return;
    
    const LODOP = getLodopInstance();
    LODOP.PRINT_INIT("水洗标签打印");
    
    // 设置纸张尺寸 - 确保使用正确的101mm × 16mm尺寸
    LODOP.SET_PRINT_PAGESIZE(1, "101mm", "16mm", "");
    
    if (!orderData || !orderData.clothes || orderData.clothes.length === 0) {
        alert('订单数据不完整，无法打印');
        return;
    }
    
    orderData.clothes.forEach((item, index) => {
        if (index > 0) {
            LODOP.NEWPAGE();
        }
        
        // 绘制外边框
        LODOP.ADD_PRINT_RECT("0.5mm", "0.5mm", "100mm", "15mm", 0, 1);
        
        // 左侧条形码区域 (约40mm宽)
        // 条形码
        const barcodeContent = `${orderData.order_number}-${String(index + 1).padStart(2, '0')}`;
        LODOP.ADD_PRINT_BARCODE(
            "1.5mm",   // Top - 距顶部1.5mm
            "1mm",     // Left - 距左边1mm  
            "38mm",    // Width - 条形码宽度38mm
            "12mm",    // Height - 条形码高度12mm
            "128A",    // 条码类型
            barcodeContent
        );
        
        // 设置条码样式
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 6);
        LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 1); // 显示条码文字
        
        // 右侧信息区域 (从40mm开始到99mm)
        // 绘制信息区域分隔线
        LODOP.ADD_PRINT_LINE("0.5mm", "40mm", "15.5mm", "40mm", 0, 1);
        
        // 第一行：衣物名称和数量、价格
        const itemName = `${item.name || '未知'}${item.color ? `(${item.color})` : ''}`;
        const quantity = item.quantity || 1;
        const priceText = `¥${(item.price || 0).toFixed(2)}件 ${quantity}/1`;
        
        // 衣物名称 (左侧)
        LODOP.ADD_PRINT_TEXT("1mm", "41mm", "35mm", "3.5mm", itemName);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        // 价格信息 (右侧)
        LODOP.ADD_PRINT_TEXT("1mm", "76mm", "23mm", "3.5mm", priceText);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐
        
        // 第二行：客户信息
        const phoneDisplay = orderData.customer_phone ? 
            `串: ${orderData.customer_phone.substr(0, 3)}****${orderData.customer_phone.substr(-4)}` : 
            '串: 未知';
        const customerName = orderData.customer_name || '未知';
        const customerInfo = `${phoneDisplay} ${customerName}`;
        
        LODOP.ADD_PRINT_TEXT("4.5mm", "41mm", "58mm", "3mm", customerInfo);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 7);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        // 第三行：订单号和日期
        const dateFormatted = orderData.date ? 
            orderData.date.split(' ')[0].replace(/-/g, '').substr(2) : 
            '未知';
        const orderInfo = `营: ${orderData.operator || '未知'} 下单: ${dateFormatted}`;
        
        LODOP.ADD_PRINT_TEXT("7.5mm", "41mm", "58mm", "3mm", orderInfo);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 7);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        // 第四行：服务和备注信息
        let serviceAndRemarks = '';
        
        // 添加服务信息
        if (item.services && Array.isArray(item.services)) {
            const services = item.services.filter(s => s !== '加急');
            if (services.length > 0) {
                serviceAndRemarks += `改衣  备注: ${services.join('/')}`;
            }
            
            // 加急标记
            if (item.services.includes('加急')) {
                serviceAndRemarks += ' [急]';
            }
        } else {
            serviceAndRemarks = '改衣  备注: ';
        }
        
        // 配饰标记
        if (item.name && (item.name.includes('配饰') || item.name.includes('饰品'))) {
            serviceAndRemarks += ' [配]';
        }
        
        // 瑕疵和备注信息
        const remarks = [];
        if (item.flaw && item.flaw.trim()) {
            remarks.push(`瑕疵:${item.flaw.trim()}`);
        }
        if (item.remarks && item.remarks.trim()) {
            remarks.push(item.remarks.trim());
        }
        if (orderData.remarks && orderData.remarks.trim()) {
            remarks.push(orderData.remarks.trim());
        }
        
        if (remarks.length > 0) {
            if (!serviceAndRemarks.includes('备注:')) {
                serviceAndRemarks += ` 备注: ${remarks.join(' ')}`;
            } else {
                serviceAndRemarks += remarks.join(' ');
            }
        }
        
        // 限制文本长度，避免超出标签范围
        if (serviceAndRemarks.length > 60) {
            serviceAndRemarks = serviceAndRemarks.substring(0, 57) + '...';
        }
        
        LODOP.ADD_PRINT_TEXT("10.5mm", "41mm", "58mm", "4mm", serviceAndRemarks);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 6);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        // 急件标志 (在右上角显示)
        if (item.services && item.services.some(s => s.includes('急件') || s.includes('加急'))) {
            LODOP.ADD_PRINT_TEXT("1mm", "95mm", "5mm", "3mm", "[急]");
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#FF0000");
        }
    });
    
    LODOP.PREVIEW();
}

// 不干胶标签打印函数 (72mm × 50mm)
function printStickyLabelLodop(orderData) {
    if (!checkLodopAvailable()) return;
    
    const LODOP = getLodopInstance();
    LODOP.PRINT_INIT("不干胶标签打印");
    
    // 设置纸张尺寸 - 修正为毫米字符串格式 (72mm × 50mm)
    LODOP.SET_PRINT_PAGESIZE(1, "72mm", "50mm", "");
    
    if (!orderData) {
        alert('订单数据不完整，无法打印');
        return;
    }
    
    // 绘制边框 - 使用毫米坐标
    LODOP.ADD_PRINT_RECT("1mm", "1mm", "70mm", "48mm", 0, 1);
    
    // 店铺名称
    LODOP.ADD_PRINT_TEXT("3mm", "1mm", "70mm", "6mm", "快速收衣");
    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 16);
    LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
    LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
    
    // 订单号
    LODOP.ADD_PRINT_TEXT("10mm", "3mm", "66mm", "5mm", `订单号：${orderData.order_number}`);
    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
    LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
    LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
    
    // 日期和电话
    const dateStr = new Date(orderData.date).toLocaleDateString('zh-CN');
    LODOP.ADD_PRINT_TEXT("16mm", "3mm", "32mm", "4mm", `日期：${dateStr}`);
    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
    
    LODOP.ADD_PRINT_TEXT("16mm", "37mm", "32mm", "4mm", `电话：${orderData.customer_phone}`);
    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3);
    
    // 客户姓名
    LODOP.ADD_PRINT_TEXT("21mm", "3mm", "32mm", "4mm", `客户：${orderData.customer_name}`);
    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
    
    // 营业员
    LODOP.ADD_PRINT_TEXT("21mm", "37mm", "32mm", "4mm", `营业员：${orderData.operator}`);
    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3);
    
    // 分割线
    LODOP.ADD_PRINT_LINE("26mm", "3mm", "26mm", "69mm", 0, 1);
    
    // 衣物列表
    let yPos = 28; // 28mm开始
    if (orderData.clothes && orderData.clothes.length > 0) {
        orderData.clothes.forEach((item, index) => {
            const itemText = `${index + 1}. ${item.name} × ${item.quantity || 1} ¥${item.price}`;
            LODOP.ADD_PRINT_TEXT(`${yPos}mm`, "3mm", "66mm", "4mm", itemText);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
            yPos += 4;
        });
    }
    
    // 总金额
    LODOP.ADD_PRINT_TEXT(`${yPos + 2}mm`, "3mm", "66mm", "5mm", `总计：¥${orderData.total_amount}`);
    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
    LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3);
    
    // 底部条码
    const barcodeData = orderData.order_number;
    LODOP.ADD_PRINT_BARCODE(`${yPos + 8}mm`, "16mm", "40mm", "8mm", "CODE128", barcodeData);
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
    LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
    
    LODOP.PREVIEW();
}

// 获取订单数据的函数 - 修正API路径
async function fetchOrderData(orderId) {
    try {
        // 修正API路径：使用order_details而不是customer_history
        const response = await fetch(`/order_details?id=${orderId}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        return data;
    } catch (error) {
        console.error('获取订单数据失败:', error);
        throw error;
    }
}

// 统一打印接口 - 带模式选择
function printWithModeSelection(orderId, printType) {
    if (!orderId) {
        alert('订单ID不能为空');
        return;
    }
    
    // 显示打印模式选择对话框
    const mode = confirm('选择打印模式：\n确定 = Lodop打印（推荐，专业打印效果）\n取消 = 网页打印（备选方案）');
    
    if (mode) {
        // Lodop打印
        fetchOrderData(orderId).then(orderData => {
            switch(printType) {
                case 'receipt':
                    printReceiptLodop(orderData);
                    break;
                case 'wash_label':
                    printWashLabelLodop(orderData);
                    break;
                case 'sticky_label':
                    printStickyLabelLodop(orderData);
                    break;
                default:
                    alert('未知的打印类型');
            }
        }).catch(error => {
            alert(`获取订单数据失败: ${error.message}`);
        });
    } else {
        // 网页打印
        switch(printType) {
            case 'receipt':
                window.open(`/receipt/${orderId}`, '_blank');
                break;
            case 'wash_label':
                window.open(`/labels/${orderId}`, '_blank');
                break;
            case 'sticky_label':
                // 跳转到不干胶打印页面
                window.open(`/sticky_label_print?order_id=${orderId}`, '_blank');
                break;
            default:
                alert('未知的打印类型');
        }
    }
}

// 导出的公共函数
window.printReceiptWithModeSelection = function(orderId) {
    printWithModeSelection(orderId, 'receipt');
};

window.printWashLabelWithModeSelection = function(orderId) {
    printWithModeSelection(orderId, 'wash_label');
};

window.printStickyLabelWithModeSelection = function(orderId) {
    printWithModeSelection(orderId, 'sticky_label');
};

// 批量打印功能
window.batchPrintWithLodop = function(orderIds, printType) {
    if (!checkLodopAvailable()) return;
    
    if (!orderIds || orderIds.length === 0) {
        alert('请选择要打印的订单');
        return;
    }
    
    const confirmMsg = `确定要批量打印 ${orderIds.length} 个订单的${printType === 'receipt' ? '收据' : (printType === 'wash_label' ? '水洗标签' : '不干胶标签')}吗？`;
    if (!confirm(confirmMsg)) {
        return;
    }
    
    // 批量获取订单数据并打印
    Promise.all(orderIds.map(orderId => fetchOrderData(orderId)))
        .then(orderDataList => {
            const LODOP = getLodopInstance();
            LODOP.PRINT_INIT(`批量${printType === 'receipt' ? '收据' : (printType === 'wash_label' ? '水洗标签' : '不干胶标签')}打印`);
            
            orderDataList.forEach((orderData, index) => {
                if (index > 0) {
                    LODOP.NewPage();
                }
                
                // 根据打印类型调用相应的打印函数
                switch(printType) {
                    case 'receipt':
                        printReceiptLodop(orderData);
                        break;
                    case 'wash_label':
                        printWashLabelLodop(orderData);
                        break;
                    case 'sticky_label':
                        printStickyLabelLodop(orderData);
                        break;
                }
            });
            
            LODOP.PREVIEW();
        })
        .catch(error => {
            alert(`批量获取订单数据失败: ${error.message}`);
        });
}; 