#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试和验证脚本
用于测试优化前后的性能差异
"""

import time
import requests
import statistics
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime

class PerformanceTester:
    def __init__(self, base_url="http://localhost:5000", session_cookie=None):
        self.base_url = base_url
        self.session = requests.Session()
        if session_cookie:
            self.session.cookies.update(session_cookie)
        
        self.results = {
            'mall_customers': [],
            'members': [],
            'history': []
        }
    
    def login(self, username="admin", password="admin123"):
        """登录获取会话"""
        login_data = {
            'username': username,
            'password': password
        }
        
        response = self.session.post(f"{self.base_url}/login", data=login_data)
        if response.status_code == 200:
            print("✅ 登录成功")
            return True
        else:
            print("❌ 登录失败")
            return False
    
    def test_mall_customers_api(self, iterations=10):
        """测试商场客户API性能"""
        print(f"\n🧪 测试商场客户API性能 ({iterations}次)")
        
        test_cases = [
            {'params': {}, 'name': '无筛选条件'},
            {'params': {'search': '商场'}, 'name': '搜索测试'},
            {'params': {'status': '活跃'}, 'name': '状态筛选'},
            {'params': {'page': 2, 'per_page': 20}, 'name': '分页测试'},
            {'params': {'search': '测试', 'status': '活跃', 'page': 1}, 'name': '复合查询'}
        ]
        
        for test_case in test_cases:
            print(f"\n  📊 测试场景: {test_case['name']}")
            times = []
            
            for i in range(iterations):
                start_time = time.time()
                
                response = self.session.get(
                    f"{self.base_url}/api/mall_customers",
                    params=test_case['params']
                )
                
                end_time = time.time()
                duration = (end_time - start_time) * 1000  # 转换为毫秒
                
                if response.status_code == 200:
                    data = response.json()
                    server_time = data.get('execution_time', 0) * 1000
                    times.append({
                        'total_time': duration,
                        'server_time': server_time,
                        'network_time': duration - server_time,
                        'record_count': len(data.get('customers', []))
                    })
                    print(f"    第{i+1}次: {duration:.2f}ms (服务器: {server_time:.2f}ms)")
                else:
                    print(f"    第{i+1}次: 请求失败 ({response.status_code})")
            
            if times:
                avg_total = statistics.mean([t['total_time'] for t in times])
                avg_server = statistics.mean([t['server_time'] for t in times])
                avg_network = statistics.mean([t['network_time'] for t in times])
                
                print(f"  📈 平均响应时间: {avg_total:.2f}ms")
                print(f"  🖥️  平均服务器时间: {avg_server:.2f}ms")
                print(f"  🌐 平均网络时间: {avg_network:.2f}ms")
                
                self.results['mall_customers'].append({
                    'test_case': test_case['name'],
                    'avg_total_time': avg_total,
                    'avg_server_time': avg_server,
                    'avg_network_time': avg_network,
                    'times': times
                })
    
    def test_members_api(self, iterations=10):
        """测试会员API性能"""
        print(f"\n🧪 测试会员API性能 ({iterations}次)")
        
        test_cases = [
            {'params': {}, 'name': '无筛选条件'},
            {'params': {'search': '138'}, 'name': '手机号搜索'},
            {'params': {'status': 'active'}, 'name': '活跃会员筛选'},
            {'params': {'status': 'inactive'}, 'name': '不活跃会员筛选'},
            {'params': {'page': 2, 'per_page': 15}, 'name': '分页测试'}
        ]
        
        for test_case in test_cases:
            print(f"\n  📊 测试场景: {test_case['name']}")
            times = []
            
            for i in range(iterations):
                start_time = time.time()
                
                response = self.session.get(
                    f"{self.base_url}/api/members",
                    params=test_case['params']
                )
                
                end_time = time.time()
                duration = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    data = response.json()
                    server_time = data.get('execution_time', 0) * 1000
                    times.append({
                        'total_time': duration,
                        'server_time': server_time,
                        'network_time': duration - server_time,
                        'record_count': len(data.get('members', []))
                    })
                    print(f"    第{i+1}次: {duration:.2f}ms (服务器: {server_time:.2f}ms)")
                else:
                    print(f"    第{i+1}次: 请求失败 ({response.status_code})")
            
            if times:
                avg_total = statistics.mean([t['total_time'] for t in times])
                avg_server = statistics.mean([t['server_time'] for t in times])
                
                print(f"  📈 平均响应时间: {avg_total:.2f}ms")
                print(f"  🖥️  平均服务器时间: {avg_server:.2f}ms")
                
                self.results['members'].append({
                    'test_case': test_case['name'],
                    'avg_total_time': avg_total,
                    'avg_server_time': avg_server,
                    'times': times
                })
    
    def test_history_api(self, iterations=5):
        """测试历史订单API性能"""
        print(f"\n🧪 测试历史订单API性能 ({iterations}次)")
        
        test_cases = [
            {'params': {'all': 'true'}, 'name': '查询所有订单'},
            {'params': {'phone': '138'}, 'name': '手机号查询'},
            {'params': {'date': '2024-01-01'}, 'name': '日期查询'},
            {'params': {'status': '已完成'}, 'name': '状态查询'},
            {'params': {'operator': '张三'}, 'name': '营业员查询'}
        ]
        
        for test_case in test_cases:
            print(f"\n  📊 测试场景: {test_case['name']}")
            times = []
            
            for i in range(iterations):
                start_time = time.time()
                
                response = self.session.get(
                    f"{self.base_url}/customer_history",
                    params=test_case['params'],
                    headers={'Accept': 'application/json'}
                )
                
                end_time = time.time()
                duration = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('found'):
                        record_count = len(data.get('orders', []))
                        times.append({
                            'total_time': duration,
                            'record_count': record_count
                        })
                        print(f"    第{i+1}次: {duration:.2f}ms (记录数: {record_count})")
                    else:
                        print(f"    第{i+1}次: {duration:.2f}ms (无记录)")
                else:
                    print(f"    第{i+1}次: 请求失败 ({response.status_code})")
            
            if times:
                avg_total = statistics.mean([t['total_time'] for t in times])
                print(f"  📈 平均响应时间: {avg_total:.2f}ms")
                
                self.results['history'].append({
                    'test_case': test_case['name'],
                    'avg_total_time': avg_total,
                    'times': times
                })
    
    def concurrent_test(self, endpoint, params=None, concurrent_users=5, requests_per_user=10):
        """并发测试"""
        print(f"\n🚀 并发测试: {endpoint} (用户数: {concurrent_users}, 每用户请求数: {requests_per_user})")
        
        def make_request(user_id):
            times = []
            for i in range(requests_per_user):
                start_time = time.time()
                response = self.session.get(f"{self.base_url}{endpoint}", params=params or {})
                end_time = time.time()
                
                duration = (end_time - start_time) * 1000
                times.append({
                    'user_id': user_id,
                    'request_id': i,
                    'duration': duration,
                    'status_code': response.status_code
                })
            return times
        
        all_times = []
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(make_request, user_id) for user_id in range(concurrent_users)]
            
            for future in as_completed(futures):
                all_times.extend(future.result())
        
        # 分析结果
        successful_requests = [t for t in all_times if t['status_code'] == 200]
        failed_requests = [t for t in all_times if t['status_code'] != 200]
        
        if successful_requests:
            durations = [t['duration'] for t in successful_requests]
            avg_duration = statistics.mean(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            p95_duration = sorted(durations)[int(len(durations) * 0.95)]
            
            print(f"  ✅ 成功请求: {len(successful_requests)}")
            print(f"  ❌ 失败请求: {len(failed_requests)}")
            print(f"  📊 平均响应时间: {avg_duration:.2f}ms")
            print(f"  ⚡ 最快响应: {min_duration:.2f}ms")
            print(f"  🐌 最慢响应: {max_duration:.2f}ms")
            print(f"  📈 95%响应时间: {p95_duration:.2f}ms")
            print(f"  🔄 QPS: {len(successful_requests) / (max_duration / 1000):.2f}")
    
    def generate_report(self):
        """生成性能测试报告"""
        print("\n📋 生成性能测试报告...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"performance_report_{timestamp}.json"
        
        report = {
            'timestamp': timestamp,
            'test_results': self.results,
            'summary': self._generate_summary()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 报告已保存到: {report_file}")
        return report_file
    
    def _generate_summary(self):
        """生成测试摘要"""
        summary = {}
        
        for api_name, results in self.results.items():
            if results:
                avg_times = [r['avg_total_time'] for r in results]
                summary[api_name] = {
                    'test_count': len(results),
                    'avg_response_time': statistics.mean(avg_times),
                    'min_response_time': min(avg_times),
                    'max_response_time': max(avg_times)
                }
        
        return summary
    
    def plot_results(self):
        """绘制性能测试结果图表"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
            matplotlib.rcParams['axes.unicode_minus'] = False
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('API性能测试结果', fontsize=16)
            
            # 商场客户API结果
            if self.results['mall_customers']:
                ax1 = axes[0, 0]
                test_names = [r['test_case'] for r in self.results['mall_customers']]
                avg_times = [r['avg_total_time'] for r in self.results['mall_customers']]
                
                ax1.bar(test_names, avg_times)
                ax1.set_title('商场客户API响应时间')
                ax1.set_ylabel('响应时间 (ms)')
                ax1.tick_params(axis='x', rotation=45)
            
            # 会员API结果
            if self.results['members']:
                ax2 = axes[0, 1]
                test_names = [r['test_case'] for r in self.results['members']]
                avg_times = [r['avg_total_time'] for r in self.results['members']]
                
                ax2.bar(test_names, avg_times)
                ax2.set_title('会员API响应时间')
                ax2.set_ylabel('响应时间 (ms)')
                ax2.tick_params(axis='x', rotation=45)
            
            # 历史订单API结果
            if self.results['history']:
                ax3 = axes[1, 0]
                test_names = [r['test_case'] for r in self.results['history']]
                avg_times = [r['avg_total_time'] for r in self.results['history']]
                
                ax3.bar(test_names, avg_times)
                ax3.set_title('历史订单API响应时间')
                ax3.set_ylabel('响应时间 (ms)')
                ax3.tick_params(axis='x', rotation=45)
            
            # 综合对比
            ax4 = axes[1, 1]
            api_names = []
            api_avg_times = []
            
            for api_name, results in self.results.items():
                if results:
                    api_names.append(api_name)
                    avg_times = [r['avg_total_time'] for r in results]
                    api_avg_times.append(statistics.mean(avg_times))
            
            if api_names:
                ax4.bar(api_names, api_avg_times)
                ax4.set_title('各API平均响应时间对比')
                ax4.set_ylabel('响应时间 (ms)')
            
            plt.tight_layout()
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_file = f"performance_plot_{timestamp}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存到: {plot_file}")
            
        except ImportError:
            print("⚠️  matplotlib未安装，跳过图表生成")


def main():
    """主函数"""
    print("🚀 启动性能测试...")
    
    tester = PerformanceTester()
    
    # 登录
    if not tester.login():
        print("❌ 无法登录，测试终止")
        return
    
    # 执行各项测试
    tester.test_mall_customers_api(iterations=5)
    tester.test_members_api(iterations=5)
    tester.test_history_api(iterations=3)
    
    # 并发测试
    tester.concurrent_test('/api/mall_customers', concurrent_users=3, requests_per_user=5)
    tester.concurrent_test('/api/members', concurrent_users=3, requests_per_user=5)
    
    # 生成报告
    report_file = tester.generate_report()
    tester.plot_results()
    
    print("\n✅ 性能测试完成！")
    print(f"📄 详细报告: {report_file}")


if __name__ == "__main__":
    main()
