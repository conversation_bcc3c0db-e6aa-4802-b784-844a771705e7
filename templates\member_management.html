<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 会员管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/print-styles.css') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #007BFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
        }
        .staff-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        .staff-info span {
            margin-right: 15px;
            color: white;
        }
        .logout-btn {
            color: #fff;
            text-decoration: none;
            font-weight: 500;
        }
        .logout-btn:hover {
            color: #f8f9fa;
            text-decoration: underline;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        .page-title {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-title h2 {
            margin: 0;
            color: #333;
        }
        .search-bar {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .search-bar input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 300px;
        }
        .search-bar select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 150px;
        }
        .search-bar button {
            padding: 8px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .member-list {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 4px;
        }
        .member-list th, .member-list td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .member-list th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .member-list tbody tr:hover {
            background-color: #f9f9f9;
        }
        .member-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
        }
        .status-active {
            background-color: #e6fff0;
            color: #52c41a;
        }
        .status-inactive {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        .balance-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
            font-size: 0.85rem;
        }
        .view-btn {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .edit-btn {
            background-color: #fff8e6;
            color: #f5a623;
        }
        .delete-btn {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .recharge-btn {
            background-color: #e6fff0;
            color: #52c41a;
        }
        .discount-btn {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .modal-header {
            background-color: #007BFF;
            color: white;
        }
        .modal-footer {
            border-top: 1px solid #eee;
            padding: 15px;
        }
        .form-group label {
            font-weight: bold;
        }
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination button {
            padding: 8px 15px;
            margin: 0 5px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #007BFF;
            color: white;
            border-color: #007BFF;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Soulweave改衣坊管理系统</h1>
        <div class="header-controls">
            <div class="staff-info">
                <span>操作员: <span id="staffName">加载中...</span></span>
            </div>
            <a href="/logout" class="logout-btn">退出登录</a>
        </div>
    </div>

    <div class="container">
        <div class="page-title">
            <h2>会员管理</h2>
            <div>
                <button class="btn btn-success" onclick="showGiftRulesModal()" style="margin-right: 10px;">
                    <i class="fas fa-gift"></i> 赠送规则管理
                </button>
                <button class="btn btn-primary" onclick="showAddMemberModal()">
                    <i class="fas fa-plus"></i> 添加会员
                </button>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="输入会员姓名或电话号码搜索">
            <select id="statusFilter">
                <option value="all">所有状态</option>
                <option value="active">活跃</option>
                <option value="inactive">非活跃</option>
            </select>
            <button onclick="searchMembers()">搜索</button>
        </div>

        <!-- 会员列表 -->
        <table class="member-list">
            <thead>
                <tr>
                    <th>会员ID</th>
                    <th>姓名</th>
                    <th>电话</th>
                    <th>账户余额</th>
                    <th>注册时间</th>
                    <th>最近更新</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="memberTableBody">
                <!-- 会员数据将在这里动态加载 -->
            </tbody>
        </table>

        <!-- 分页控件 -->
        <div class="pagination" id="paginationContainer">
            <!-- 分页按钮将在这里动态加载 -->
        </div>
    </div>

    <!-- 添加会员模态框 -->
    <div class="modal fade" id="addMemberModal" tabindex="-1" role="dialog" aria-labelledby="addMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addMemberModalLabel">添加会员</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addMemberForm">
                        <div class="form-group">
                            <label for="memberName" class="required-field">会员姓名</label>
                            <input type="text" class="form-control" id="memberName" required>
                        </div>
                        <div class="form-group">
                            <label for="memberPhone" class="required-field">电话号码</label>
                            <input type="text" class="form-control" id="memberPhone" required>
                        </div>
                        <div class="form-group">
                            <label for="memberBalance">账户余额</label>
                            <input type="number" class="form-control" id="memberBalance" step="0.01" min="0" value="0">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveMemberBtn" onclick="saveMember()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 会员详情模态框 -->
    <div class="modal fade" id="memberDetailModal" tabindex="-1" role="dialog" aria-labelledby="memberDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="memberDetailModalLabel">会员详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="memberDetailTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="info-tab" data-toggle="tab" href="#info" role="tab" aria-controls="info" aria-selected="true">基本信息</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="orders-tab" data-toggle="tab" href="#orders" role="tab" aria-controls="orders" aria-selected="false">订单历史</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="recharge-tab" data-toggle="tab" href="#recharge" role="tab" aria-controls="recharge" aria-selected="false">充值记录</a>
                        </li>
                    </ul>
                    <div class="tab-content" id="memberDetailTabContent">
                        <!-- 基本信息标签页 -->
                        <div class="tab-pane fade show active" id="info" role="tabpanel" aria-labelledby="info-tab">
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <p><strong>会员ID:</strong> <span id="detailMemberId"></span></p>
                                    <p><strong>姓名:</strong> <span id="detailMemberName"></span></p>
                                    <p><strong>电话:</strong> <span id="detailMemberPhone"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>充值余额:</strong> <span id="detailMemberBalance"></span></p>
                                    <p><strong>赠送余额:</strong> <span id="detailMemberGiftBalance"></span></p>
                                    <p><strong>总余额:</strong> <span id="detailMemberTotalBalance" style="color: #28a745; font-weight: bold;"></span></p>
                                    <p><strong>注册时间:</strong> <span id="detailMemberCreatedAt"></span></p>
                                    <p><strong>最近更新:</strong> <span id="detailMemberUpdatedAt"></span></p>
                                </div>
                            </div>
                        </div>

                        <!-- 订单历史标签页 -->
                        <div class="tab-pane fade" id="orders" role="tabpanel" aria-labelledby="orders-tab">
                            <table class="table table-striped mt-3">
                                <thead>
                                    <tr>
                                        <th>订单编号</th>
                                        <th>日期</th>
                                        <th>金额</th>
                                        <th>支付方式</th>
                                        <th>支付状态</th>
                                        <th>订单状态</th>
                                    </tr>
                                </thead>
                                <tbody id="orderHistoryTableBody">
                                    <!-- 订单历史将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 充值记录标签页 -->
                        <div class="tab-pane fade" id="recharge" role="tabpanel" aria-labelledby="recharge-tab">
                            <table class="table table-striped mt-3">
                                <thead>
                                    <tr>
                                        <th>充值ID</th>
                                        <th>日期</th>
                                        <th>充值金额</th>
                                        <th>赠送金额</th>
                                        <th>支付方式</th>
                                        <th>操作员</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody id="rechargeHistoryTableBody">
                                    <!-- 充值记录将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" id="rechargeMemberBtn" onclick="showRechargeModal()">充值</button>
                    <button type="button" class="btn btn-primary" id="editMemberBtn" onclick="showEditMemberModal()">编辑</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑会员模态框 -->
    <div class="modal fade" id="editMemberModal" tabindex="-1" role="dialog" aria-labelledby="editMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editMemberModalLabel">编辑会员</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editMemberForm">
                        <input type="hidden" id="editMemberId">
                        <div class="form-group">
                            <label for="editMemberName" class="required-field">会员姓名</label>
                            <input type="text" class="form-control" id="editMemberName" required>
                        </div>
                        <div class="form-group">
                            <label for="editMemberPhone" class="required-field">电话号码</label>
                            <input type="text" class="form-control" id="editMemberPhone" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="updateMemberBtn" onclick="updateMember()">更新</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 充值模态框 -->
    <div class="modal fade" id="rechargeModal" tabindex="-1" role="dialog" aria-labelledby="rechargeModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rechargeModalLabel">账户充值</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="rechargeForm">
                        <input type="hidden" id="rechargeMemberId">
                        <div class="form-group">
                            <label for="rechargeMemberNameDisplay">会员姓名</label>
                            <input type="text" class="form-control" id="rechargeMemberNameDisplay" readonly>
                        </div>
                        <div class="form-group">
                            <label for="rechargeMemberPhoneDisplay">电话号码</label>
                            <input type="text" class="form-control" id="rechargeMemberPhoneDisplay" readonly>
                        </div>
                        <div class="form-group">
                            <label for="currentBalanceDisplay">当前余额</label>
                            <input type="text" class="form-control" id="currentBalanceDisplay" readonly>
                        </div>
                        <div class="form-group">
                            <label for="rechargeAmount" class="required-field">充值金额</label>
                            <input type="number" class="form-control" id="rechargeAmount" step="0.01" min="0" required>
                        </div>
                        <div class="form-group" id="giftRulesContainer" style="display: none;">
                            <label>选择赠送规则（可选）</label>
                            <div id="giftRulesOptions">
                                <!-- 赠送规则选项将在这里动态加载 -->
                            </div>
                            <small class="form-text text-muted">
                                不选择规则时将自动使用最优赠送规则
                            </small>
                        </div>
                        <div class="form-group">
                            <label>预计赠送金额</label>
                            <div class="form-control-plaintext" id="giftAmountDisplay" style="color: #28a745; font-weight: bold;">¥0.00</div>
                        </div>
                        <div class="form-group">
                            <label for="paymentMethod" class="required-field">支付方式</label>
                            <select class="form-control" id="paymentMethod" required>
                                <option value="扫银联码">扫银联码</option>
                                <option value="商场POS">商场POS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="rechargeRemarks">备注</label>
                            <textarea class="form-control" id="rechargeRemarks" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitRechargeBtn" onclick="submitRecharge()">确认充值</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这个会员吗？这个操作不可逆。</p>
                    <input type="hidden" id="deleteMemberId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" onclick="confirmDelete()">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 充值赠送规则管理模态框 -->
    <div class="modal fade" id="giftRulesModal" tabindex="-1" role="dialog" aria-labelledby="giftRulesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="giftRulesModalLabel">充值赠送规则管理</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" onclick="showAddRuleModal()">
                            <i class="fas fa-plus"></i> 添加规则
                        </button>
                    </div>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>最小金额</th>
                                <th>赠送类型</th>
                                <th>赠送值</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="giftRulesTableBody">
                            <!-- 规则列表将在这里动态加载 -->
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑规则模态框 -->
    <div class="modal fade" id="addRuleModal" tabindex="-1" role="dialog" aria-labelledby="addRuleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRuleModalLabel">添加赠送规则</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="ruleForm">
                        <input type="hidden" id="ruleId">
                        <div class="form-group">
                            <label for="minAmount" class="required-field">最小充值金额</label>
                            <input type="number" class="form-control" id="minAmount" step="0.01" min="0" required>
                            <small class="form-text text-muted">充值金额达到此值时可享受赠送</small>
                        </div>
                        <div class="form-group">
                            <label for="giftType" class="required-field">赠送类型</label>
                            <select class="form-control" id="giftType" required>
                                <option value="percentage">按百分比赠送</option>
                                <option value="fixed">固定金额赠送</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="giftValue" class="required-field">赠送值</label>
                            <input type="number" class="form-control" id="giftValue" step="0.01" min="0" required>
                            <small class="form-text text-muted" id="giftValueHint">百分比赠送请输入0-100之间的数值</small>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="isActive" checked>
                                <label class="form-check-label" for="isActive">启用此规则</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveRuleBtn" onclick="saveRule()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 会员折扣管理模态框 -->
    <div class="modal fade" id="discountManagementModal" tabindex="-1" role="dialog" aria-labelledby="discountManagementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="discountManagementModalLabel">会员折扣管理</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 会员信息显示 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">会员信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>姓名:</strong> <span id="discountMemberName"></span></p>
                                    <p><strong>电话:</strong> <span id="discountMemberPhone"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>整体折扣率:</strong> <span id="discountMemberRate"></span></p>
                                    <p><strong>折扣有效期:</strong> <span id="discountMemberExpiry"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 整体折扣设置 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">整体折扣设置</h6>
                        </div>
                        <div class="card-body">
                            <form id="overallDiscountForm">
                                <input type="hidden" id="discountMemberId">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="overallDiscountRate">整体折扣率</label>
                                            <input type="number" class="form-control" id="overallDiscountRate"
                                                   step="0.01" min="0" max="1" placeholder="1.0表示无折扣，0.8表示8折">
                                            <small class="form-text text-muted">输入0-1之间的数值，如0.8表示8折</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="overallDiscountExpiry">折扣有效期</label>
                                            <input type="date" class="form-control" id="overallDiscountExpiry">
                                            <small class="form-text text-muted">留空表示永久有效</small>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="updateOverallDiscount()">更新整体折扣</button>
                            </form>
                        </div>
                    </div>

                    <!-- 服务类型折扣设置 -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">服务类型折扣</h6>
                            <button type="button" class="btn btn-sm btn-primary" onclick="showAddServiceDiscountModal()">
                                <i class="fas fa-plus"></i> 添加服务折扣
                            </button>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>服务类型</th>
                                        <th>折扣率</th>
                                        <th>生效日期</th>
                                        <th>失效日期</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="serviceDiscountsTableBody">
                                    <!-- 服务折扣列表将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑服务折扣模态框 -->
    <div class="modal fade" id="serviceDiscountModal" tabindex="-1" role="dialog" aria-labelledby="serviceDiscountModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="serviceDiscountModalLabel">添加服务折扣</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="serviceDiscountForm">
                        <input type="hidden" id="serviceDiscountId">
                        <input type="hidden" id="serviceDiscountMemberId">
                        <div class="form-group">
                            <label for="serviceType" class="required-field">服务类型</label>
                            <select class="form-control" id="serviceType" required>
                                <option value="">请选择服务类型</option>
                                <option value="洗衣">洗衣</option>
                                <option value="织补">织补</option>
                                <option value="改衣">改衣</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="serviceDiscountRate" class="required-field">折扣率</label>
                            <input type="number" class="form-control" id="serviceDiscountRate"
                                   step="0.01" min="0" max="1" required placeholder="0.8表示8折">
                            <small class="form-text text-muted">输入0-1之间的数值，如0.8表示8折</small>
                        </div>
                        <div class="form-group">
                            <label for="validFrom" class="required-field">生效日期</label>
                            <input type="date" class="form-control" id="validFrom" required>
                        </div>
                        <div class="form-group">
                            <label for="validTo" class="required-field">失效日期</label>
                            <input type="date" class="form-control" id="validTo" required>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="serviceDiscountActive" checked>
                                <label class="form-check-label" for="serviceDiscountActive">启用此折扣</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveServiceDiscountBtn" onclick="saveServiceDiscount()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
    <script src="{{ url_for('static', filename='js/print-functions.js') }}"></script>

    <script>
        // 全局变量
        let currentMemberId = null;
        let members = [];
        let currentPage = 1;
        let totalPages = 1;

        // 页面加载完成后执行
        $(document).ready(function() {
            // 显示操作员姓名
            $('#staffName').text('{{ staff_name }}');

            // 加载会员列表
            loadMembers();

            // 绑定搜索事件
            $('#searchInput').on('keyup', function(e) {
                if (e.key === 'Enter') {
                    searchMembers();
                }
            });

            // 绑定状态筛选事件
            $('#statusFilter').on('change', function() {
                searchMembers();
            });
        });

        // 加载会员列表
        function loadMembers(page = 1, searchTerm = '', statusFilter = 'all') {
            currentPage = page;

            // 显示加载指示器
            $('#memberTableBody').html('<tr><td colspan="8" class="text-center">加载中...</td></tr>');

            // 构建请求参数
            let params = new URLSearchParams();
            params.append('page', page);

            if (searchTerm) {
                params.append('search', searchTerm);
            }

            if (statusFilter !== 'all') {
                params.append('status', statusFilter);
            }

            // 发送API请求
            fetch('/api/members?' + params.toString())
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应失败');
                    }
                    return response.json();
                })
                .then(data => {
                    members = data.members;
                    totalPages = data.total_pages;

                    // 渲染会员列表
                    renderMembers(members);

                    // 渲染分页控件
                    renderPagination(data);
                })
                .catch(error => {
                    console.error('加载会员失败:', error);
                    $('#memberTableBody').html('<tr><td colspan="8" class="text-center text-danger">加载失败，请重试</td></tr>');
                });
        }

        // 渲染会员列表
        function renderMembers(members) {
            if (!members || members.length === 0) {
                $('#memberTableBody').html('<tr><td colspan="8" class="text-center">没有找到会员记录</td></tr>');
                return;
            }

            let html = '';

            members.forEach(member => {
                // 确定会员状态（基于最后更新时间，超过180天未更新视为非活跃）
                const lastUpdated = new Date(member.updated_at);
                const now = new Date();
                const daysSinceUpdate = Math.floor((now - lastUpdated) / (1000 * 60 * 60 * 24));
                const status = daysSinceUpdate <= 180 ? 'active' : 'inactive';
                const statusText = status === 'active' ? '活跃' : '非活跃';
                const statusClass = status === 'active' ? 'status-active' : 'status-inactive';

                html += `
                <tr>
                    <td>${member.id}</td>
                    <td>${member.name}</td>
                    <td>${member.phone}</td>
                    <td><span class="balance-badge">¥${member.total_balance.toFixed(2)}</span></td>
                    <td>${new Date(member.created_at).toLocaleString()}</td>
                    <td>${new Date(member.updated_at).toLocaleString()}</td>
                    <td><span class="member-status ${statusClass}">${statusText}</span></td>
                    <td>
                        <button class="action-btn view-btn" onclick="showMemberDetail(${member.id})">查看</button>
                        <button class="action-btn edit-btn" onclick="showEditMemberModal(${member.id})">编辑</button>
                        <button class="action-btn recharge-btn" onclick="showRechargeModal(${member.id})">充值</button>
                        <button class="action-btn discount-btn" onclick="showDiscountManagementModal(${member.id})">折扣</button>
                        <button class="action-btn delete-btn" onclick="showDeleteConfirmModal(${member.id})">删除</button>
                    </td>
                </tr>
                `;
            });

            $('#memberTableBody').html(html);
        }

        // 渲染分页控件
        function renderPagination(data) {
            const totalPages = data.total_pages;
            const currentPage = data.current_page;

            let html = '';

            // 上一页按钮
            html += `<button onclick="goToPage(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>上一页</button>`;

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                html += `<button onclick="goToPage(${i})" class="${i === currentPage ? 'active' : ''}">${i}</button>`;
            }

            // 下一页按钮
            html += `<button onclick="goToPage(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>下一页</button>`;

            $('#paginationContainer').html(html);
        }

        // 跳转到指定页
        function goToPage(page) {
            if (page < 1 || page > totalPages) {
                return;
            }

            const searchTerm = $('#searchInput').val();
            const statusFilter = $('#statusFilter').val();

            loadMembers(page, searchTerm, statusFilter);
        }

        // 搜索会员
        function searchMembers() {
            const searchTerm = $('#searchInput').val();
            const statusFilter = $('#statusFilter').val();

            loadMembers(1, searchTerm, statusFilter);
        }

        // 显示添加会员模态框
        function showAddMemberModal() {
            // 重置表单
            $('#addMemberForm')[0].reset();

            // 显示模态框
            $('#addMemberModal').modal('show');
        }

        // 保存新会员
        function saveMember() {
            // 收集表单数据
            const memberData = {
                name: $('#memberName').val(),
                phone: $('#memberPhone').val(),
                balance: parseFloat($('#memberBalance').val() || 0)
            };

            // 表单验证
            if (!memberData.name || !memberData.phone) {
                alert('请填写必填字段');
                return;
            }

            // 发送API请求
            fetch('/api/members', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(memberData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '添加会员失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 关闭模态框
                $('#addMemberModal').modal('hide');

                // 重新加载会员列表
                loadMembers();

                // 显示成功消息
                alert('会员添加成功');
            })
            .catch(error => {
                console.error('添加会员失败:', error);
                alert(error.message || '添加会员失败，请重试');
            });
        }

        // 显示会员详情
        function showMemberDetail(memberId) {
            currentMemberId = memberId;

            // 显示加载中状态
            $('#detailMemberId').text('加载中...');
            $('#detailMemberName').text('加载中...');
            $('#detailMemberPhone').text('加载中...');
            $('#detailMemberBalance').text('加载中...');
            $('#detailMemberCreatedAt').text('加载中...');
            $('#detailMemberUpdatedAt').text('加载中...');

            // 清空订单历史和充值记录
            $('#orderHistoryTableBody').html('<tr><td colspan="6" class="text-center">加载中...</td></tr>');
            $('#rechargeHistoryTableBody').html('<tr><td colspan="6" class="text-center">加载中...</td></tr>');

            // 显示模态框
            $('#memberDetailModal').modal('show');

            // 加载会员详情
            fetch(`/api/members/${memberId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取会员详情失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 更新会员基本信息
                    $('#detailMemberId').text(data.id);
                    $('#detailMemberName').text(data.name);
                    $('#detailMemberPhone').text(data.phone);
                    $('#detailMemberBalance').text(`¥${data.balance.toFixed(2)}`);
                    $('#detailMemberGiftBalance').text(`¥${(data.gift_balance || 0).toFixed(2)}`);
                    $('#detailMemberTotalBalance').text(`¥${data.total_balance.toFixed(2)}`);
                    $('#detailMemberCreatedAt').text(new Date(data.created_at).toLocaleString());
                    $('#detailMemberUpdatedAt').text(new Date(data.updated_at).toLocaleString());

                    // 加载订单历史
                    loadMemberOrders(memberId);

                    // 加载充值记录
                    loadMemberRechargeRecords(memberId);
                })
                .catch(error => {
                    console.error('获取会员详情失败:', error);
                    $('#memberDetailModal').modal('hide');
                    alert('获取会员详情失败，请重试');
                });
        }

        // 加载会员的订单历史
        function loadMemberOrders(memberId) {
            fetch(`/api/members/${memberId}/orders`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取订单历史失败');
                    }
                    return response.json();
                })
                .then(data => {
                    renderOrderHistory(data.orders);
                })
                .catch(error => {
                    console.error('获取订单历史失败:', error);
                    $('#orderHistoryTableBody').html('<tr><td colspan="6" class="text-center text-danger">加载失败，请重试</td></tr>');
                });
        }

        // 渲染订单历史
        function renderOrderHistory(orders) {
            if (!orders || orders.length === 0) {
                $('#orderHistoryTableBody').html('<tr><td colspan="6" class="text-center">暂无订单记录</td></tr>');
                return;
            }

            let html = '';

            orders.forEach(order => {
                html += `
                <tr>
                    <td>${order.order_number}</td>
                    <td>${new Date(order.created_at).toLocaleString()}</td>
                    <td>¥${order.total_amount.toFixed(2)}</td>
                    <td>${order.payment_method}</td>
                    <td>${order.payment_status}</td>
                    <td>${order.status}</td>
                </tr>
                `;
            });

            $('#orderHistoryTableBody').html(html);
        }

        // 加载会员的充值记录
        function loadMemberRechargeRecords(memberId) {
            fetch(`/api/members/${memberId}/recharges`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取充值记录失败');
                    }
                    return response.json();
                })
                .then(data => {
                    renderRechargeHistory(data.recharges);
                })
                .catch(error => {
                    console.error('获取充值记录失败:', error);
                    $('#rechargeHistoryTableBody').html('<tr><td colspan="7" class="text-center text-danger">加载失败，请重试</td></tr>');
                });
        }

        // 渲染充值历史
        function renderRechargeHistory(recharges) {
            if (!recharges || recharges.length === 0) {
                $('#rechargeHistoryTableBody').html('<tr><td colspan="7" class="text-center">暂无充值记录</td></tr>');
                return;
            }

            let html = '';

            recharges.forEach(recharge => {
                html += `
                <tr>
                    <td>${recharge.id}</td>
                    <td>${new Date(recharge.created_at).toLocaleString()}</td>
                    <td>¥${recharge.amount.toFixed(2)}</td>
                    <td style="color: #28a745;">¥${(recharge.gift_amount || 0).toFixed(2)}</td>
                    <td>${recharge.payment_method}</td>
                    <td>${recharge.operator || '未知'}</td>
                    <td>${recharge.remarks || '-'}</td>
                </tr>
                `;
            });

            $('#rechargeHistoryTableBody').html(html);
        }

        // 显示编辑会员模态框
        function showEditMemberModal(memberId = null) {
            // 如果没有指定会员ID，使用当前选择的会员ID
            if (memberId === null) {
                memberId = currentMemberId;
            } else {
                currentMemberId = memberId;
            }

            // 如果之前已经打开了详情模态框，关闭它
            $('#memberDetailModal').modal('hide');

            // 获取会员信息
            const member = members.find(m => m.id === memberId);

            if (!member) {
                // 如果在当前页面找不到该会员，从服务器获取
                fetch(`/api/members/${memberId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('获取会员信息失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // 填充表单
                        $('#editMemberId').val(data.id);
                        $('#editMemberName').val(data.name);
                        $('#editMemberPhone').val(data.phone);

                        // 显示模态框
                        $('#editMemberModal').modal('show');
                    })
                    .catch(error => {
                        console.error('获取会员信息失败:', error);
                        alert('获取会员信息失败，请重试');
                    });
            } else {
                // 填充表单
                $('#editMemberId').val(member.id);
                $('#editMemberName').val(member.name);
                $('#editMemberPhone').val(member.phone);

                // 显示模态框
                $('#editMemberModal').modal('show');
            }
        }

        // 更新会员信息
        function updateMember() {
            const memberId = $('#editMemberId').val();

            // 收集表单数据
            const memberData = {
                name: $('#editMemberName').val(),
                phone: $('#editMemberPhone').val()
            };

            // 表单验证
            if (!memberData.name || !memberData.phone) {
                alert('请填写必填字段');
                return;
            }

            // 发送API请求
            fetch(`/api/members/${memberId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(memberData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '更新会员失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 关闭模态框
                $('#editMemberModal').modal('hide');

                // 重新加载会员列表
                loadMembers(currentPage);

                // 显示成功消息
                alert('会员信息已更新');
            })
            .catch(error => {
                console.error('更新会员失败:', error);
                alert(error.message || '更新会员失败，请重试');
            });
        }

        // 显示充值模态框
        function showRechargeModal(memberId = null) {
            // 如果没有指定会员ID，使用当前选择的会员ID
            if (memberId === null) {
                memberId = currentMemberId;
            } else {
                currentMemberId = memberId;
            }

            // 如果之前已经打开了详情模态框，关闭它
            $('#memberDetailModal').modal('hide');

            // 重置表单
            $('#rechargeForm')[0].reset();
            $('#rechargeMemberId').val(memberId);

            // 获取会员信息
            fetch(`/api/members/${memberId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取会员信息失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 填充表单
                    $('#rechargeMemberNameDisplay').val(data.name);
                    $('#rechargeMemberPhoneDisplay').val(data.phone);
                    $('#currentBalanceDisplay').val(`充值余额: ¥${data.balance.toFixed(2)} | 赠送余额: ¥${data.gift_balance.toFixed(2)} | 总余额: ¥${data.total_balance.toFixed(2)}`);

                    // 显示模态框
                    $('#rechargeModal').modal('show');

                    // 绑定充值金额变化事件
                    $('#rechargeAmount').off('input').on('input', function() {
                        const amount = parseFloat($(this).val()) || 0;
                        calculateGiftOptions(amount);
                    });
                })
                .catch(error => {
                    console.error('获取会员信息失败:', error);
                    alert('获取会员信息失败，请重试');
                });
        }

        // 计算赠送金额
        function calculateGiftAmount() {
            const amount = parseFloat($('#rechargeAmount').val()) || 0;

            if (amount <= 0) {
                $('#giftAmountDisplay').text('¥0.00');
                return;
            }

            // 调用API计算赠送金额
            fetch('/api/calculate_gift', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ amount: amount })
            })
            .then(response => response.json())
            .then(data => {
                if (data.gift_amount !== undefined) {
                    $('#giftAmountDisplay').text(`¥${data.gift_amount.toFixed(2)}`);
                } else {
                    $('#giftAmountDisplay').text('¥0.00');
                }
            })
            .catch(error => {
                console.error('计算赠送金额失败:', error);
                $('#giftAmountDisplay').text('¥0.00');
            });
        }

        // 计算充值赠送选项
        function calculateGiftOptions(amount) {
            if (!amount || amount <= 0) {
                $('#giftRulesContainer').hide();
                $('#giftAmountDisplay').text('¥0.00');
                return;
            }

            fetch('/api/calculate_gift_options', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ amount: amount })
            })
            .then(response => response.json())
            .then(data => {
                if (data.options && data.options.length > 0) {
                    // 显示规则选择区域
                    $('#giftRulesContainer').show();

                    // 渲染规则选项
                    let html = '';

                    // 添加"不使用规则"选项
                    html += `
                        <div class="form-check" style="text-align: left;">
                            <input class="form-check-input gift-rule-option" type="radio" name="giftRule"
                                   id="rule_none" value="" data-gift-amount="0">
                            <label class="form-check-label" for="rule_none" style="font-size: 0.9em; text-align: left;">
                                不使用赠送规则 (赠送¥0.00)
                            </label>
                        </div>
                    `;

                    data.options.forEach((option, index) => {
                        const isRecommended = data.recommended && option.rule_id === data.recommended.rule_id;
                        const checkedAttr = index === 0 ? 'checked' : ''; // 默认选择第一个（最优）
                        const recommendedBadge = isRecommended ? '<span class="badge badge-success ml-2">推荐</span>' : '';

                        html += `
                            <div class="form-check" style="text-align: left;">
                                <input class="form-check-input gift-rule-option" type="radio" name="giftRule"
                                       id="rule_${option.rule_id}" value="${option.rule_id}"
                                       data-gift-amount="${option.gift_amount}" ${checkedAttr}>
                                <label class="form-check-label" for="rule_${option.rule_id}" style="font-size: 0.9em; text-align: left;">
                                    ${option.description} (赠送¥${option.gift_amount.toFixed(2)})${recommendedBadge}
                                </label>
                            </div>
                        `;
                    });

                    $('#giftRulesOptions').html(html);

                    // 绑定选择事件
                    $('.gift-rule-option').on('change', function() {
                        const giftAmount = parseFloat($(this).data('gift-amount'));
                        $('#giftAmountDisplay').text(`¥${giftAmount.toFixed(2)}`);
                    });

                    // 设置默认赠送金额
                    const defaultGiftAmount = data.options[0].gift_amount;
                    $('#giftAmountDisplay').text(`¥${defaultGiftAmount.toFixed(2)}`);
                } else {
                    $('#giftRulesContainer').hide();
                    $('#giftAmountDisplay').text('¥0.00');
                }
            })
            .catch(error => {
                console.error('计算赠送选项失败:', error);
                $('#giftRulesContainer').hide();
                $('#giftAmountDisplay').text('¥0.00');
            });
        }

        // 提交充值
        function submitRecharge() {
            const memberId = $('#rechargeMemberId').val();

            // 收集表单数据
            const rechargeData = {
                amount: parseFloat($('#rechargeAmount').val()),
                payment_method: $('#paymentMethod').val(),
                remarks: $('#rechargeRemarks').val()
            };

            // 获取选择的规则
            const selectedRule = $('input[name="giftRule"]:checked');
            if (selectedRule.length > 0) {
                if (selectedRule.val() === '') {
                    // 用户选择了"不使用规则"
                    rechargeData.no_gift_rule = true;
                } else {
                    // 用户选择了特定规则
                    rechargeData.selected_rule_id = parseInt(selectedRule.val());
                }
            }

            // 表单验证
            if (!rechargeData.amount || rechargeData.amount <= 0) {
                alert('请输入有效的充值金额');
                return;
            }

            if (!rechargeData.payment_method) {
                alert('请选择支付方式');
                return;
            }

            // 发送API请求
            fetch(`/api/members/${memberId}/recharge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(rechargeData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '充值失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 关闭模态框
                $('#rechargeModal').modal('hide');

                // 重新加载会员列表
                loadMembers(currentPage);

                // 准备充值小票数据
                const memberName = $('#rechargeMemberNameDisplay').val();
                const memberPhone = $('#rechargeMemberPhoneDisplay').val();
                const rechargeReceiptData = {
                    customer_name: memberName,
                    phone: memberPhone,
                    amount: rechargeData.amount,
                    giftAmount: data.gift_amount || 0,
                    paymentMethod: rechargeData.payment_method,
                    newBalance: data.new_balance,
                    isNewCustomer: false,
                    operator: data.operator || '{{ session.staff_name or "系统" }}'
                };

                // 显示成功消息
                const giftMessage = data.gift_amount > 0 ? `，赠送金额: ¥${data.gift_amount.toFixed(2)}` : '';
                const successMessage = `充值成功，新余额: ¥${data.new_balance.toFixed(2)}${giftMessage}`;
                
                // 询问是否打印小票
                if (confirm(successMessage + '\n\n是否立即打印充值小票？')) {
                    // 用户选择打印小票
                    console.log('准备打印充值小票，数据:', rechargeReceiptData);
                    console.log('printRechargeReceipt 函数是否存在:', typeof printRechargeReceipt);
                    
                    if (typeof printRechargeReceipt === 'function') {
                        try {
                            printRechargeReceipt(rechargeReceiptData);
                        } catch (error) {
                            console.error('打印充值小票出错:', error);
                            alert('打印功能出错: ' + error.message);
                        }
                    } else {
                        alert('打印功能不可用，请稍后重试');
                        console.error('printRechargeReceipt 函数未定义');
                    }
                } else {
                    // 用户选择不打印
                    alert(successMessage);
                }
            })
            .catch(error => {
                console.error('充值失败:', error);
                alert(error.message || '充值失败，请重试');
            });
        }

        // 显示删除确认模态框
        function showDeleteConfirmModal(memberId) {
            currentMemberId = memberId;
            $('#deleteMemberId').val(memberId);
            $('#deleteModal').modal('show');
        }

        // 确认删除会员
        function confirmDelete() {
            const memberId = $('#deleteMemberId').val();

            // 发送API请求
            fetch(`/api/members/${memberId}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '删除会员失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 关闭模态框
                $('#deleteModal').modal('hide');

                // 重新加载会员列表
                loadMembers(currentPage);

                // 显示成功消息
                alert('会员已删除');
            })
            .catch(error => {
                console.error('删除会员失败:', error);
                alert(error.message || '删除会员失败，请重试');
            });
        }

        // ===== 充值赠送规则管理功能 =====

        // 显示规则管理模态框
        function showGiftRulesModal() {
            loadGiftRules();
            $('#giftRulesModal').modal('show');
        }

        // 加载赠送规则列表
        function loadGiftRules() {
            $('#giftRulesTableBody').html('<tr><td colspan="6" class="text-center">加载中...</td></tr>');

            fetch('/api/recharge_gift_rules')
                .then(response => response.json())
                .then(data => {
                    renderGiftRules(data.rules);
                })
                .catch(error => {
                    console.error('加载规则失败:', error);
                    $('#giftRulesTableBody').html('<tr><td colspan="6" class="text-center text-danger">加载失败，请重试</td></tr>');
                });
        }

        // 渲染规则列表
        function renderGiftRules(rules) {
            if (!rules || rules.length === 0) {
                $('#giftRulesTableBody').html('<tr><td colspan="6" class="text-center">暂无规则</td></tr>');
                return;
            }

            let html = '';
            rules.forEach(rule => {
                const giftTypeText = rule.gift_type === 'percentage' ? '百分比' : '固定金额';
                const giftValueText = rule.gift_type === 'percentage' ? `${rule.gift_value}%` : `¥${rule.gift_value}`;
                const statusText = rule.is_active ? '启用' : '禁用';
                const statusClass = rule.is_active ? 'text-success' : 'text-muted';

                html += `
                    <tr>
                        <td>¥${rule.min_amount}</td>
                        <td>${giftTypeText}</td>
                        <td>${giftValueText}</td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                        <td>${new Date(rule.created_at).toLocaleString()}</td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="editRule(${rule.id})">编辑</button>
                            <button class="btn btn-sm btn-${rule.is_active ? 'warning' : 'success'}"
                                    onclick="toggleRuleStatus(${rule.id}, ${!rule.is_active})">
                                ${rule.is_active ? '禁用' : '启用'}
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteRule(${rule.id})">删除</button>
                        </td>
                    </tr>
                `;
            });

            $('#giftRulesTableBody').html(html);
        }

        // 显示添加规则模态框
        function showAddRuleModal() {
            $('#addRuleModalLabel').text('添加赠送规则');
            $('#ruleForm')[0].reset();
            $('#ruleId').val('');
            $('#isActive').prop('checked', true);
            updateGiftValueHint();
            $('#addRuleModal').modal('show');
        }

        // 编辑规则
        function editRule(ruleId) {
            fetch(`/api/recharge_gift_rules`)
                .then(response => response.json())
                .then(data => {
                    const rule = data.rules.find(r => r.id === ruleId);
                    if (rule) {
                        $('#addRuleModalLabel').text('编辑赠送规则');
                        $('#ruleId').val(rule.id);
                        $('#minAmount').val(rule.min_amount);
                        $('#giftType').val(rule.gift_type);
                        $('#giftValue').val(rule.gift_value);
                        $('#isActive').prop('checked', rule.is_active);
                        updateGiftValueHint();
                        $('#addRuleModal').modal('show');
                    }
                })
                .catch(error => {
                    console.error('获取规则详情失败:', error);
                    alert('获取规则详情失败，请重试');
                });
        }

        // 保存规则
        function saveRule() {
            const ruleId = $('#ruleId').val();
            const isEdit = !!ruleId;

            const ruleData = {
                min_amount: parseFloat($('#minAmount').val()),
                gift_type: $('#giftType').val(),
                gift_value: parseFloat($('#giftValue').val()),
                is_active: $('#isActive').prop('checked')
            };

            // 表单验证
            if (!ruleData.min_amount || ruleData.min_amount <= 0) {
                alert('请输入有效的最小充值金额');
                return;
            }

            if (!ruleData.gift_value || ruleData.gift_value <= 0) {
                alert('请输入有效的赠送值');
                return;
            }

            if (ruleData.gift_type === 'percentage' && ruleData.gift_value > 100) {
                alert('百分比赠送不能超过100%');
                return;
            }

            const url = isEdit ? `/api/recharge_gift_rules/${ruleId}` : '/api/recharge_gift_rules';
            const method = isEdit ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(ruleData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '保存规则失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                $('#addRuleModal').modal('hide');
                loadGiftRules();
                alert(isEdit ? '规则更新成功' : '规则创建成功');
            })
            .catch(error => {
                console.error('保存规则失败:', error);
                alert(error.message || '保存规则失败，请重试');
            });
        }

        // 切换规则状态
        function toggleRuleStatus(ruleId, newStatus) {
            fetch(`/api/recharge_gift_rules/${ruleId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ is_active: newStatus })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '更新规则状态失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                loadGiftRules();
                alert(`规则已${newStatus ? '启用' : '禁用'}`);
            })
            .catch(error => {
                console.error('更新规则状态失败:', error);
                alert(error.message || '更新规则状态失败，请重试');
            });
        }

        // 删除规则
        function deleteRule(ruleId) {
            if (!confirm('确定要删除这个规则吗？此操作不可逆。')) {
                return;
            }

            fetch(`/api/recharge_gift_rules/${ruleId}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '删除规则失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                loadGiftRules();
                alert('规则删除成功');
            })
            .catch(error => {
                console.error('删除规则失败:', error);
                alert(error.message || '删除规则失败，请重试');
            });
        }

        // 更新赠送值提示
        function updateGiftValueHint() {
            const giftType = $('#giftType').val();
            const hint = giftType === 'percentage' ?
                '百分比赠送请输入0-100之间的数值' :
                '固定金额赠送请输入具体金额';
            $('#giftValueHint').text(hint);
        }

        // 绑定赠送类型变化事件
        $(document).ready(function() {
            $('#giftType').on('change', updateGiftValueHint);
        });

        // =====================================================================
        // 会员折扣管理功能
        // =====================================================================

        // 显示折扣管理模态框
        function showDiscountManagementModal(memberId) {
            currentMemberId = memberId;

            // 加载会员折扣信息
            loadMemberDiscounts(memberId);

            // 显示模态框
            $('#discountManagementModal').modal('show');
        }

        // 加载会员折扣信息
        function loadMemberDiscounts(memberId) {
            fetch(`/api/members/${memberId}/discounts`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 显示会员基本信息
                    $('#discountMemberName').text(data.member.name);
                    $('#discountMemberPhone').text(data.member.phone);
                    $('#discountMemberRate').text((data.member.discount_rate * 100).toFixed(0) + '%');
                    $('#discountMemberExpiry').text(data.member.discount_expiry || '永久有效');

                    // 设置隐藏字段
                    $('#discountMemberId').val(data.member.id);

                    // 填充整体折扣表单
                    $('#overallDiscountRate').val(data.member.discount_rate);
                    $('#overallDiscountExpiry').val(data.member.discount_expiry || '');

                    // 渲染服务折扣列表
                    renderServiceDiscounts(data.discounts);
                })
                .catch(error => {
                    console.error('加载会员折扣失败:', error);
                    alert('加载会员折扣信息失败，请重试');
                });
        }

        // 渲染服务折扣列表
        function renderServiceDiscounts(discounts) {
            if (!discounts || discounts.length === 0) {
                $('#serviceDiscountsTableBody').html('<tr><td colspan="6" class="text-center">暂无服务折扣</td></tr>');
                return;
            }

            let html = '';
            discounts.forEach(discount => {
                const statusText = discount.is_active ? '启用' : '禁用';
                const statusClass = discount.is_active ? 'text-success' : 'text-muted';

                html += `
                <tr>
                    <td>${discount.service_type}</td>
                    <td>${(discount.discount_rate * 100).toFixed(0)}%</td>
                    <td>${discount.valid_from}</td>
                    <td>${discount.valid_to}</td>
                    <td><span class="${statusClass}">${statusText}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editServiceDiscount(${discount.id})">编辑</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteServiceDiscount(${discount.id})">删除</button>
                    </td>
                </tr>
                `;
            });

            $('#serviceDiscountsTableBody').html(html);
        }

        // 更新整体折扣
        function updateOverallDiscount() {
            const memberId = $('#discountMemberId').val();
            const discountRate = parseFloat($('#overallDiscountRate').val()) || 1.0;
            const discountExpiry = $('#overallDiscountExpiry').val() || null;

            // 验证数据
            if (discountRate <= 0 || discountRate > 1) {
                alert('折扣率必须在0-1之间');
                return;
            }

            const data = {
                discount_rate: discountRate,
                discount_expiry: discountExpiry
            };

            fetch(`/api/members/${memberId}/overall_discount`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '更新整体折扣失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 更新显示
                $('#discountMemberRate').text((data.member.discount_rate * 100).toFixed(0) + '%');
                $('#discountMemberExpiry').text(data.member.discount_expiry || '永久有效');

                // 重新加载会员列表
                loadMembers(currentPage);

                alert('整体折扣更新成功');
            })
            .catch(error => {
                console.error('更新整体折扣失败:', error);
                alert(error.message || '更新整体折扣失败，请重试');
            });
        }

        // 显示添加服务折扣模态框
        function showAddServiceDiscountModal() {
            // 重置表单
            $('#serviceDiscountForm')[0].reset();
            $('#serviceDiscountId').val('');
            $('#serviceDiscountMemberId').val($('#discountMemberId').val());

            // 设置模态框标题
            $('#serviceDiscountModalLabel').text('添加服务折扣');

            // 显示模态框
            $('#serviceDiscountModal').modal('show');
        }

        // 编辑服务折扣
        function editServiceDiscount(discountId) {
            // 从当前数据中找到对应的折扣
            fetch(`/api/members/${$('#discountMemberId').val()}/discounts`)
                .then(response => response.json())
                .then(data => {
                    const discount = data.discounts.find(d => d.id === discountId);
                    if (!discount) {
                        throw new Error('找不到指定的折扣');
                    }

                    // 填充表单
                    $('#serviceDiscountId').val(discount.id);
                    $('#serviceDiscountMemberId').val($('#discountMemberId').val());
                    $('#serviceType').val(discount.service_type);
                    $('#serviceDiscountRate').val(discount.discount_rate);
                    $('#validFrom').val(discount.valid_from);
                    $('#validTo').val(discount.valid_to);
                    $('#serviceDiscountActive').prop('checked', discount.is_active);

                    // 设置模态框标题
                    $('#serviceDiscountModalLabel').text('编辑服务折扣');

                    // 显示模态框
                    $('#serviceDiscountModal').modal('show');
                })
                .catch(error => {
                    console.error('加载折扣信息失败:', error);
                    alert('加载折扣信息失败，请重试');
                });
        }

        // 保存服务折扣
        function saveServiceDiscount() {
            const discountId = $('#serviceDiscountId').val();
            const memberId = $('#serviceDiscountMemberId').val();
            const isEdit = !!discountId;

            // 收集表单数据
            const data = {
                service_type: $('#serviceType').val(),
                discount_rate: parseFloat($('#serviceDiscountRate').val()),
                valid_from: $('#validFrom').val(),
                valid_to: $('#validTo').val(),
                is_active: $('#serviceDiscountActive').is(':checked')
            };

            // 验证数据
            if (!data.service_type || !data.discount_rate || !data.valid_from || !data.valid_to) {
                alert('请填写所有必填字段');
                return;
            }

            if (data.discount_rate <= 0 || data.discount_rate > 1) {
                alert('折扣率必须在0-1之间');
                return;
            }

            if (new Date(data.valid_from) >= new Date(data.valid_to)) {
                alert('生效日期必须早于失效日期');
                return;
            }

            // 发送请求
            const url = isEdit ? `/api/member_discounts/${discountId}` : `/api/members/${memberId}/discounts`;
            const method = isEdit ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '保存服务折扣失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 关闭模态框
                $('#serviceDiscountModal').modal('hide');

                // 重新加载折扣列表
                loadMemberDiscounts(memberId);

                alert(isEdit ? '服务折扣更新成功' : '服务折扣创建成功');
            })
            .catch(error => {
                console.error('保存服务折扣失败:', error);
                alert(error.message || '保存服务折扣失败，请重试');
            });
        }

        // 删除服务折扣
        function deleteServiceDiscount(discountId) {
            if (!confirm('确定要删除这个服务折扣吗？此操作不可逆。')) {
                return;
            }

            fetch(`/api/member_discounts/${discountId}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '删除服务折扣失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                // 重新加载折扣列表
                loadMemberDiscounts($('#discountMemberId').val());

                alert('服务折扣删除成功');
            })
            .catch(error => {
                console.error('删除服务折扣失败:', error);
                alert(error.message || '删除服务折扣失败，请重试');
            });
        }
    </script>
</body>
</html>
